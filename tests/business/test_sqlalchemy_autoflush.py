#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
测试 SQLAlchemy Auto-Flush 行为

本测试验证当修改了一个模型实例的属性后，执行新的数据库查询时，
SQLAlchemy 会自动 flush dirty 对象到数据库。
"""
import pytest
from flask import g

from app.models.user import P2pUser, User
from app.models import db
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang

USER_ID = 20073


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestSQLAlchemyAutoFlush:

    def create_test_data(self, tcontext):
        """创建测试数据的辅助方法"""
        from app.models.mongo.p2p.order import P2pOrderCreateSnapMySQL
        test_order_id = 999999
        snap = P2pOrderCreateSnapMySQL(
            order_id=test_order_id,
            user_info={"123": "test_user", "456": "another_user"},
            adv={"test": "data"},
            pay_channel={"channel": "test"}
        )
        db.session.add(snap)
        db.session.commit()
        return test_order_id
        

    def test_autoflush_on_query(self, tcontext):
        """
        测试 SQLAlchemy 的 auto-flush 行为
        
        当修改一个对象的属性后，该对象会被标记为 dirty。
        在执行新的数据库查询前，SQLAlchemy 会自动 flush dirty 对象。
        """
        with tcontext:
            from app.models.mongo.p2p.order import P2pOrderCreateSnapMySQL

            # 步骤 1: 创建测试数据
            test_order_id = self.create_test_data(tcontext)

            # 步骤 2: 查询对象并修改其属性（模拟 get_by_order_id 中的行为）
            item = P2pOrderCreateSnapMySQL.query.filter(
                P2pOrderCreateSnapMySQL.order_id == test_order_id
            ).first()
            
            assert item.user_info == {"123": "test_user", "456": "another_user"}
        
            # 修改 user_info 属性（这会使对象变为 dirty）
            item.user_info = {int(k): v for k, v in item.user_info.items()}
            
            # 验证对象已被标记为 dirty
            assert item in db.session.dirty
            
            # 步骤 3: 执行新的数据库查询（这会触发 auto-flush）
            # 使用 SQLAlchemy 事件系统来追踪 flush 是否被调用
            from sqlalchemy import event
            
            # 创建一个标志来追踪 flush 是否被调用
            flush_events = {"count": 0}
            
            def on_after_flush(session, flush_context):
                """flush 事件监听器"""
                flush_events["count"] += 1
            
            # 注册 flush 事件监听器
            event.listen(db.session, "after_flush", on_after_flush)
            
            try:
                # 模拟调用 P2pUtils.get_biz_user_id_map
                P2pUser.query.filter(
                    P2pUser.user_id.in_([USER_ID])
                ).with_entities(
                    P2pUser.user_id,
                    P2pUser.biz_user_id
                ).all()
                
                # 验证 flush 事件被触发
                assert flush_events["count"] >= 1, \
                    f"auto-flush 应该被触发至少一次，实际触发了 {flush_events['count']} 次"
                
                # 打印调试信息
                print(f"\n[调试] flush 被调用了 {flush_events['count']} 次")
                
            finally:
                # 移除事件监听器
                event.remove(db.session, "after_flush", on_after_flush)
            
            # 步骤 4: 验证 auto-flush 已发生
            # 在执行查询后，dirty 对象应该已经被 flush
            # 注意：在 patch_mysql 模式下，flush 会触发但数据会在 nested transaction 中
            
            # 重新查询数据库以验证更改
            item_after = P2pOrderCreateSnapMySQL.query.filter(
                P2pOrderCreateSnapMySQL.order_id == test_order_id
            ).first()
            
            # 验证数据已更新（键从字符串变为整数）
            assert item_after.user_info == {123: "test_user", 456: "another_user"}
            
    def test_expunge_not_track_object(self, tcontext):
        """
        测试 SQLAlchemy expunge 后对象的行为
        
        当调用 expunge() 后：
        1. 对象从 session 中被移除，状态变为 detached
        2. 修改对象属性不会被 session 跟踪
        3. 对象不在 session.dirty 集合中
        4. flush 不会保存对 expunge 对象的修改
        """
        with tcontext:
            from app.models.mongo.p2p.order import P2pOrderCreateSnapMySQL

            # 步骤 1: 创建测试数据
            test_id = self.create_test_data(tcontext)
            
            # 步骤 2: 查询对象
            item = P2pOrderCreateSnapMySQL.query.filter(
                P2pOrderCreateSnapMySQL.order_id == test_id
            ).first()
            
            # 步骤 3: 调用 expunge() 将对象从 session 中移除
            db.session.expunge(item)
            
            item.user_info = {int(k): v for k, v in item.user_info.items()}
            
            # 验证对象不在 dirty 集合中
            assert item not in db.session.dirty, "expunge 后的对象修改不应该被 session 跟踪"
            
            # 步骤 5: 提交（不会保存对 expunge 对象的修改）
            db.session.commit()
            
            # 模拟调用 P2pUtils.get_biz_user_id_map
            P2pUser.query.filter(
                P2pUser.user_id.in_([USER_ID])
            ).with_entities(
                P2pUser.user_id,
                P2pUser.biz_user_id
            ).all()
            
            # 步骤 6: 重新查询验证数据未更新
            item_after = P2pOrderCreateSnapMySQL.query.filter(
                P2pOrderCreateSnapMySQL.order_id == test_id
            ).first()
            
            # 验证数据未更新（仍然是字符串键，而不是整数键）
            assert item_after.user_info == {"123": "test_user", "456": "another_user"}, \
                "expunge 后的修改不应该被保存到数据库"
            

