#!/usr/bin/env python3
import datetime
from collections import defaultdict
from decimal import Decimal
from typing import Dict, Set, List

from celery.schedules import crontab
from sqlalchemy import func, or_

from app.business import lock_call, PerpetualSummaryDB, TradeSummaryDB
from app.business.clients.api_gateway import ApiGatewayClient
from app.business.trade import TradeSummaryRepository
from app.common import CeleryQueues
from app.models.base import read_only_session
from app.models import UserApiFrequencyLimitRecord, User, MarketMaker, SubAccount, DailyApiAccessRankReport, db, \
    UserTradeFeeSummary, AssetPrice, UserLongLimiterRecord, ApiAuth
from app.models.api_resource import ApiResource, HourApiResource, DailyApiResource, \
    ApiRequest, HourApiRequest, DailyApiRequest
from app.utils import route_module_to_celery_queue, scheduled, amount_to_str
from app.utils import today, g_map
from app.utils.date_ import convert_datetime, now, date_to_datetime, timestamp_to_datetime

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)


class ApiStaticReport:

    TOP_N = 10000
    TOP_LATEST = 24

    API_GROUP_WEIGHT_MAP = {
        # 下单、改单
        UserApiFrequencyLimitRecord.ApiGroups.ORDER: Decimal(1),
        UserApiFrequencyLimitRecord.ApiGroups.ORDERS: Decimal(1),
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER: Decimal(1),
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDERS: Decimal(1),
        # 撤单
        UserApiFrequencyLimitRecord.ApiGroups.CANCEL: Decimal(0.5),
        UserApiFrequencyLimitRecord.ApiGroups.CANCELS: Decimal(0.5),
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_CANCEL: Decimal(0.5),
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_CANCELS: Decimal(0.5),
        # 查单
        UserApiFrequencyLimitRecord.ApiGroups.QUERY_ORDER: Decimal(0.1),
        UserApiFrequencyLimitRecord.ApiGroups.QUERY_ORDER_HISTORY: Decimal(0.1),
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_QUERY_ORDER: Decimal(0.1),
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_QUERY_ORDER_HISTORY: Decimal(0.1),
    }

    def __init__(self, report_date: datetime.date, system: DailyApiAccessRankReport.System):
        self.report_date = report_date
        self.system = system
        self.trade_db = PerpetualSummaryDB if system == DailyApiAccessRankReport.System.PERPETUAL else TradeSummaryDB
        self.query_group = UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER \
            if system == DailyApiAccessRankReport.System.PERPETUAL else UserApiFrequencyLimitRecord.ApiGroups.ORDER
        self.close_rate_mapper = AssetPrice.get_close_price_map(report_date)

    def get_group_top_n_data(self, group: UserApiFrequencyLimitRecord.ApiGroups):
        api_client = ApiGatewayClient()
        return api_client.req_top_n(group, latest=self.TOP_LATEST, top_n=self.TOP_N)

    def get_trade_user_usd(self, user_ids: Set[int]):
        """获取用户交易金额"""
        records = self.trade_db.group_by_user_asset_by_date(self.report_date)
        if not records:
            return {}
        res = defaultdict(lambda: defaultdict(Decimal))
        for record in records:
            user_id = record['user_id']
            if user_id not in user_ids:
                continue
            if self.system == DailyApiAccessRankReport.System.PERPETUAL:
                res[user_id]['deal_amount'] += record['deal_amount']
                res[user_id]['maker_amount'] += record['maker_amount']
                res[user_id]['taker_amount'] += record['taker_amount']
            else:
                _rate = self.close_rate_mapper[record['money_asset']]
                res[user_id]['deal_amount'] += record['deal_volume'] * _rate
                res[user_id]['maker_amount'] += record['maker_volume'] * _rate
                res[user_id]['taker_amount'] += record['taker_volume'] * _rate
        return res

    def check_preconditions(self):
        last_row = DailyApiAccessRankReport.query.filter(
            DailyApiAccessRankReport.report_date == self.report_date,
            DailyApiAccessRankReport.system == self.system
        ).first()
        report_summary = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.report_date == self.report_date,
            UserTradeFeeSummary.system == UserTradeFeeSummary.System[self.system.name]
        ).first()
        if last_row or not report_summary:
            return False
        return True

    @classmethod
    def get_user_email_remark(cls, user_ids: Set[int]) -> Dict[int, Dict]:
        """用户email，做市商备注"""
        sub_accounts = SubAccount.query.filter(
            SubAccount.user_id.in_(user_ids)
        ).with_entities(
            SubAccount.user_id,
            SubAccount.main_user_id
        ).all()
        sub_main_dic = {i[0]: i[1] for i in sub_accounts}
        main_user_ids = (set(user_ids) - set(sub_main_dic.keys())) | set(sub_main_dic.values())
        users = User.query.filter(
            User.id.in_(main_user_ids)
        ).with_entities(
            User.id,
            User.email
        ).all()
        user_email_dic = {i[0]: i[1] for i in users}
        market_makers = MarketMaker.query.filter(
            MarketMaker.user_id.in_(main_user_ids),
            MarketMaker.status == MarketMaker.StatusType.PASS
        ).with_entities(
            MarketMaker.user_id,
            MarketMaker.remark
        ).all()
        user_remark_dic = {i[0]: i[1] for i in market_makers}

        res = dict()
        all_user_ids = set(user_ids) | set([v.user_id for v in sub_accounts]) | set(
            [v.main_user_id for v in sub_accounts])
        for user_id in all_user_ids:
            if user_id not in user_email_dic:  # 子账户
                main_user_id = sub_main_dic[user_id]
            else:  # 主账户
                main_user_id = user_id

            email = user_email_dic[main_user_id]
            remark = user_remark_dic.get(main_user_id, '')
            res.update({
                user_id: {
                    'main_user_id': main_user_id,
                    'email': email,
                    'remark': remark,
                    'accounts': len(list(filter(lambda x: x == main_user_id,
                                                sub_main_dic.values()))) + 1,
                }
            })
        return res

    def get_perpetual_fee_summary_usd(self, user_ids: Set[int]) -> Dict[int, Decimal]:
        return {uid: fee for uid, fee in UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.system == UserTradeFeeSummary.System[self.system.name],
            UserTradeFeeSummary.report_date == self.report_date,
            UserTradeFeeSummary.user_id.in_(user_ids)
        ).with_entities(
            UserTradeFeeSummary.user_id,
            UserTradeFeeSummary.trade_fee_amount
        ).all()}

    def get_user_req_mapper(self, user_ids: Set[int]) -> Dict[int, Decimal]:
        """用户流量饱和度"""
        records = DailyApiResource.query.filter(
            DailyApiResource.time == date_to_datetime(self.report_date),
            DailyApiResource.user_id.in_(user_ids),
            DailyApiResource.group == self.query_group.name,
        ).with_entities(
            DailyApiResource.user_id,
            DailyApiResource.req_count
        )
        result = defaultdict(Decimal)
        for record in records:
            result[record.user_id] = record.req_count
        return result

    def get_user_limiter(self, user_ids: Set[int]):
        group = self.query_group
        model = UserLongLimiterRecord
        user_limiter = model.query.filter(
            model.group == group,
            model.status == model.Status.VALID,
            model.user_id.in_(user_ids)
        ).with_entities(
            model.user_id,
            model.long_period_hour,
            model.limit_total_count
        ).all()
        user_limiter_mapper = defaultdict(dict)
        for i in user_limiter:
            user_limiter_mapper[i.user_id] = i.limit_total_count / i.long_period_hour

        default_limiter = model.query.filter(
            model.user_id == 0,
            model.status == model.Status.VALID,
            model.group == group,
        ).with_entities(
            model.limit_total_count / model.long_period_hour
        ).scalar() or UserApiFrequencyLimitRecord.DEFAULT_LIMIT * 3600

        result = defaultdict(int)
        for user_id in user_ids:
            result[user_id] = (user_limiter_mapper.get(user_id) or default_limiter) * 24
        return result

    def get_valid_api_user_ids(self) -> Set[int]:
        """获取所有拥有有效API KEY的用户ID（包括子账号）"""
        records = ApiAuth.query.filter(
            ApiAuth.status == ApiAuth.Status.VALID,
            or_(
                ApiAuth.expired_at.is_(None),
                ApiAuth.expired_at > now()
            )
        ).with_entities(
            ApiAuth.user_id.distinct()
        ).all()
        api_user_ids = {r[0] for r in records}

        return api_user_ids

    def domain(self):
        if not self.check_preconditions():
            return
        # 1. 获取所有拥有有效API KEY的用户ID（包括子账号）
        api_user_ids = self.get_valid_api_user_ids()

        # 2. 通过 filter_trade_users 查询昨天是否有过交易
        tomorrow = self.report_date + datetime.timedelta(days=1)
        user_ids = TradeSummaryRepository.filter_trade_users(self.report_date, tomorrow, api_user_ids)
        # 如果没有交易用户，直接返回
        if not user_ids:
            return

        # 获取这些用户的API访问数据
        api_access_data = self.get_group_top_n_data(self.query_group)
        trade_usd_data = self.get_trade_user_usd(user_ids)
        user_info = self.get_user_email_remark(user_ids)
        aggr_items = {}
        for index, (_id, count) in enumerate(api_access_data):
            email = user_info[_id]['email']
            # aggr sub-account amount
            if email not in aggr_items:
                aggr_items[email] = {
                    "main_user_id": user_info[_id]["main_user_id"],
                    "remark": user_info[_id]["remark"],
                    "active_accounts": 0,
                    "count_aggr": count,
                    "deal_amount_aggr": trade_usd_data[_id]['deal_amount'],
                    "maker_amount_aggr": trade_usd_data[_id]['maker_amount'],
                    "taker_amount_aggr": trade_usd_data[_id]['taker_amount'],
                }
            else:
                aggr_items[email]["count_aggr"] += count
                aggr_items[email]["deal_amount_aggr"] += trade_usd_data[_id]['deal_amount']
                aggr_items[email]["maker_amount_aggr"] += trade_usd_data[_id]['maker_amount']
                aggr_items[email]["taker_amount_aggr"] += trade_usd_data[_id]['taker_amount']

            # active account
            if count >= 1000:
                aggr_items[email]["active_accounts"] += 1

        items = []
        for email, data in aggr_items.items():
            items.append({
                "main_user_id": data['main_user_id'],
                "email": email,
                "remark": data['remark'],
                "count": data["count_aggr"],
                "rate": data["count_aggr"] // 86400,
                "active_accounts": data["active_accounts"],
                "deal_amount": amount_to_str(data['deal_amount_aggr'], 2),
                "maker_amount": amount_to_str(data['maker_amount_aggr'], 2),
                "taker_amount": amount_to_str(data['taker_amount_aggr'], 2),
                "resource_efficiency": amount_to_str(
                    data['deal_amount_aggr'] / data["count_aggr"], 2)
                if data["count_aggr"] else 0
            })

        items = sorted(items, key=lambda x: (x["rate"], x["count"]), reverse=True)[:100]
        main_user_ids = {item["main_user_id"] for item in items}
        trade_fee_mapper = self.get_perpetual_fee_summary_usd(set(main_user_ids))
        user_resource_data_mapper = {
            dr.user_id: dr for dr in DailyApiResource.query.filter(
                DailyApiResource.time == date_to_datetime(self.report_date),
                DailyApiResource.user_id.in_(main_user_ids),
                DailyApiResource.group == self.query_group.name,
            ).with_entities(
                DailyApiResource.user_id,
                DailyApiResource.req_count,
                DailyApiResource.rel_count,
                DailyApiResource.put_order_count,
                DailyApiResource.active_total_time
            ).all()
        }
        user_req_data_mapper = self.get_user_req_mapper(main_user_ids)
        user_limiter_mapper = self.get_user_limiter(main_user_ids)
        for index, item in enumerate(items):
            items[index].update({
                "rank": index + 1,
                "accounts": user_info[item["main_user_id"]]['accounts'],
            })
            user_id = item['main_user_id']
            trade_fee = trade_fee_mapper.get(user_id, Decimal())
            resource_data = user_resource_data_mapper.get(user_id)
            row = DailyApiAccessRankReport.get_or_create(
                report_date=self.report_date,
                system=self.system,
                user_id=user_id
            )
            db.session.add(row)
            row.count = item['count']
            row.rate = item['rate']
            row.deal_amount = item['deal_amount']
            row.maker_amount = item['maker_amount']
            row.taker_amount = item['taker_amount']
            row.resource_efficiency = item['resource_efficiency']
            row.rank = item['rank']
            row.total_trade_fee = trade_fee
            row.accounts = item['accounts']
            row.avg_active_time = int(resource_data.active_total_time / resource_data.req_count) \
                if resource_data and resource_data.req_count else 0
            row.active_accounts = item['active_accounts']
            row.effective_rate = Decimal(resource_data.rel_count / resource_data.put_order_count) \
                if resource_data and resource_data.put_order_count else Decimal('0')
            if user_limit := user_limiter_mapper.get(user_id):
                saturation_rate = user_req_data_mapper.get(user_id, 0) / user_limit
            else:
                saturation_rate = 0
            row.saturation_rate = saturation_rate
        db.session.commit()


@scheduled(crontab(minute="20,30,50", hour=1))
@lock_call()
def static_daily_perpetual_api_access_rank():
    """统计每天的合约下单&撤单api用户资源使用情况，并发送到企业微信"""
    report_date = today() - datetime.timedelta(days=1)
    ApiStaticReport(report_date, DailyApiAccessRankReport.System.PERPETUAL).domain()
    ApiStaticReport(report_date, DailyApiAccessRankReport.System.SPOT).domain()


def get_user_email_remark(user_ids: List) -> Dict[int, Dict]:
    """用户email，做市商备注"""
    sub_accounts = SubAccount.query.filter(
        SubAccount.user_id.in_(user_ids)
    ).with_entities(
        SubAccount.user_id,
        SubAccount.main_user_id
    ).all()
    sub_main_dic = {i[0]: i[1] for i in sub_accounts}
    main_user_ids = (set(user_ids) - set(sub_main_dic.keys())) | set(sub_main_dic.values())
    users = User.query.filter(
        User.id.in_(main_user_ids)
    ).with_entities(
        User.id,
        User.email
    ).all()
    user_email_dic = {i[0]: i[1] for i in users}
    market_makers = MarketMaker.query.filter(
        MarketMaker.user_id.in_(main_user_ids),
        MarketMaker.status == MarketMaker.StatusType.PASS
    ).with_entities(
        MarketMaker.user_id,
        MarketMaker.remark
    ).all()
    user_remark_dic = {i[0]: i[1] for i in market_makers}

    res = dict()
    all_user_ids = set(user_ids) | set([v.user_id for v in sub_accounts]) | set([v.main_user_id for v in sub_accounts])
    for user_id in all_user_ids:
        if user_id not in user_email_dic:   # 子账户
            main_user_id = sub_main_dic[user_id]
        else:   # 主账户
            main_user_id = user_id

        email = user_email_dic[main_user_id]
        remark = user_remark_dic.get(main_user_id, '')
        res.update({
            user_id: {
                'main_user_id': main_user_id,
                'email': email,
                'remark': remark,
                'accounts': len(list(filter(lambda x: x == main_user_id,
                                            sub_main_dic.values()))) + 1,
            }
        })
    return res


def get_perpetual_trade_usd(user_ids: List[int]) -> Dict[int, Dict]:
    """获取用户合约交易金额"""
    trade_date = today() - datetime.timedelta(days=1)
    records = PerpetualSummaryDB.group_by_user_asset_by_date(trade_date)

    res = defaultdict(lambda: defaultdict(Decimal))
    if not records:
        return res
    for record in records:
        user_id = record['user_id']
        if user_id not in user_ids:
            continue
        res[user_id]['deal_amount'] += record['deal_amount']
        res[user_id]['maker_amount'] += record['maker_amount']
        res[user_id]['taker_amount'] += record['taker_amount']
    return res


def get_or_create_by_model(model, time, group, user_id):
    if not (ar := model.query.filter(
            model.time == time,
            model.group == group,
            model.user_id == user_id
    ).first()):
        ar = model(
            time=time,
            group=group,
            user_id=user_id
        )
    return ar


def create_sys_data_by_model(model):
    # sql:28s
    with read_only_session() as ro_session:
        group_query = ro_session.query(model).filter(
            model.user_id != 0
        ).group_by(
            model.time,
            model.group
        ).with_entities(
            model.time,
            model.group,
            func.sum(model.req_count),
            func.sum(model.rel_count),
            func.sum(model.put_order_count),
            func.sum(model.active_total_time)
        ).all()
    records = []
    for time, group, sum_req, sum_rel, sum_order_count, sum_active in group_query:
        sys_record = get_or_create_by_model(model, time, group, 0)
        sys_record.req_count = sum_req
        sys_record.rel_count = sum_rel
        sys_record.put_order_count = sum_order_count
        sys_record.active_total_time = sum_active
        records.append(sys_record)
    return records


@scheduled(crontab(minute="*/5"), queue=CeleryQueues.STATISTIC)
@lock_call()
def flush_api_sources():

    need_statistics_groups = [
        UserApiFrequencyLimitRecord.ApiGroups.ORDER,
        UserApiFrequencyLimitRecord.ApiGroups.ORDERS,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDER,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_ORDERS,
        UserApiFrequencyLimitRecord.ApiGroups.CANCEL,
        UserApiFrequencyLimitRecord.ApiGroups.CANCELS,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_CANCEL,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_CANCELS,
        UserApiFrequencyLimitRecord.ApiGroups.QUERY_ORDER,
        UserApiFrequencyLimitRecord.ApiGroups.QUERY_ORDER_HISTORY,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_QUERY_ORDER,
        UserApiFrequencyLimitRecord.ApiGroups.PERPETUAL_QUERY_ORDER_HISTORY,
    ]

    now_ = now()
    source_data_count = 4

    def datetime_to_slot(dtime):
        return dtime.hour * 60 + dtime.minute

    current_slot = datetime_to_slot(now_)

    def index_to_datetime(idx):
        offset = idx - current_slot
        if offset >= 0:
            offset -= 60 * 24
        idx_time = now_ + datetime.timedelta(minutes=offset)
        return convert_datetime(idx_time, "minute")

    start_time = now_ - datetime.timedelta(days=1)

    last_data = ApiResource.query.filter(ApiResource.user_id == 0).with_entities(
        func.max(ApiResource.time).label('time')
    ).first()
    if last_data and last_data.time > start_time:
        start_time = last_data.time

    api_client = ApiGatewayClient()
    records = api_client.req_api_resource(need_statistics_groups)
    add_records = []
    for group,  user_mapper in records.items():
        for user_id, user_data in user_mapper.items():
            if not user_data:
                continue
            for i, data in enumerate(user_data):
                if any(data) is False:
                    continue
                time = index_to_datetime(i)
                if time < start_time:
                    continue
                # 兼容旧数据
                put_order_count = 0
                if len(data[0:source_data_count]) < source_data_count:
                    req_count, rel_count, active_time = data[0: source_data_count - 1]
                else:
                    req_count, rel_count, active_time, put_order_count = data[0: source_data_count]
                ar = get_or_create_by_model(ApiResource, time, group, user_id)
                ar.req_count = req_count
                ar.put_order_count = put_order_count
                ar.rel_count = rel_count
                ar.active_total_time = active_time
                add_records.append(ar)

    system_data = create_sys_data_by_model(ApiResource)
    db.session.add_all(add_records + system_data)
    db.session.commit()


@scheduled(crontab(minute="*/20"), queue=CeleryQueues.STATISTIC)
@lock_call()
def flush_hour_api_sources():
    now_ = now()
    end_time = convert_datetime(now_, "hour")
    start_time = end_time - datetime.timedelta(hours=1)
    last_data = HourApiResource.query.filter(HourApiResource.user_id == 0).with_entities(
        func.max(HourApiResource.time).label('time')
    ).first()
    if last_data:
        start_time = last_data.time

    query = ApiResource.query.filter(
        ApiResource.time >= start_time
    ).with_entities(
        ApiResource.time,
        ApiResource.group,
        ApiResource.user_id,
        ApiResource.req_count,
        ApiResource.put_order_count,
        ApiResource.rel_count,
        ApiResource.active_total_time
    ).all()
    time_data_mapper = defaultdict(lambda: defaultdict(lambda: {
        "req_count": int(),
        "put_order_count": int(),
        "rel_count": int(),
        "active_total_time": int()
    }))
    for t, group, uid, req, oc, rel, active in query:
        current_hour = convert_datetime(t, "hour")
        key = f"{group}:{uid}"
        time_data_mapper[current_hour][key]["req_count"] += req
        time_data_mapper[current_hour][key]["rel_count"] += rel
        time_data_mapper[current_hour][key]["put_order_count"] += oc
        time_data_mapper[current_hour][key]["active_total_time"] += active
    res = []
    for time, group_data in time_data_mapper.items():
        for key, data in group_data.items():
            group, user_id = str(key).split(":")
            hr = get_or_create_by_model(HourApiResource, time, group, user_id)
            hr.req_count = data["req_count"]
            hr.put_order_count = data["put_order_count"]
            hr.rel_count = data["rel_count"]
            hr.active_total_time = data["active_total_time"]
            res.append(hr)

    db.session.add_all(res)
    db.session.commit()


@scheduled(crontab(hour="*/1", minute="3"), queue=CeleryQueues.STATISTIC)
@lock_call()
def flush_daily_api_sources():
    now_ = now()
    end_time = convert_datetime(now_, "day")
    start_time = end_time - datetime.timedelta(days=1)
    last_data = DailyApiResource.query.filter(DailyApiResource.user_id == 0).with_entities(
        func.max(DailyApiResource.time).label('time')
    ).first()
    if last_data:
        start_time = last_data.time

    query = HourApiResource.query.filter(
        HourApiResource.time >= start_time
    ).with_entities(
        HourApiResource.time,
        HourApiResource.group,
        HourApiResource.user_id,
        HourApiResource.req_count,
        HourApiResource.put_order_count,
        HourApiResource.rel_count,
        HourApiResource.active_total_time
    ).all()
    time_data_mapper = defaultdict(lambda: defaultdict(lambda: {
        "req_count": int(),
        "put_order_count": int(),
        "rel_count": int(),
        "active_total_time": int()
    }))
    for t, group, uid, req, oc, rel, active in query:
        current_hour = convert_datetime(t, "day")
        key = f"{group}:{uid}"
        time_data_mapper[current_hour][key]["req_count"] += req
        time_data_mapper[current_hour][key]["put_order_count"] += oc
        time_data_mapper[current_hour][key]["rel_count"] += rel
        time_data_mapper[current_hour][key]["active_total_time"] += active

    res = []
    for time, group_data in time_data_mapper.items():
        for key, data in group_data.items():
            group, user_id = str(key).split(":")
            dr = get_or_create_by_model(DailyApiResource, time, group, user_id)
            dr.req_count = data["req_count"]
            dr.put_order_count = data["put_order_count"]
            dr.rel_count = data["rel_count"]
            dr.active_total_time = data["active_total_time"]
            res.append(dr)

    db.session.add_all(res)
    db.session.commit()


@scheduled(crontab(minute="4", hour="1"))
def clear_api_sources_data():
    now_ = now()
    ApiResource.query.filter(
        ApiResource.time < now_ - datetime.timedelta(days=7)
    ).delete()
    HourApiResource.query.filter(
        HourApiResource.time < now_ - datetime.timedelta(days=90)
    ).delete()
    db.session.commit()


@scheduled(crontab(minute="*/5"), queue=CeleryQueues.STATISTIC)
@lock_call()
def flush_api_requests():
    now_ = now()
    start_time = now_ - datetime.timedelta(days=1)
    last_data = ApiRequest.query.filter(ApiRequest.user_id == 0).with_entities(
        func.max(ApiRequest.time).label('time')
    ).first()
    if last_data:
        start_time = last_data.time + datetime.timedelta(minutes=1)

    start_ts = int(start_time.timestamp())
    end_ts = int(now_.timestamp()) - int(now_.timestamp()) % 60
    api_client = ApiGatewayClient()
    groups = [g for g in UserApiFrequencyLimitRecord.ApiGroups]
    group_users_map = defaultdict(set)
    for g in groups:
        api_access_data = api_client.req_top_n(g, 24, top_n=10000)
        user_ids = {i for i, _ in api_access_data}
        group_users_map[g] = user_ids

    time_data_mapper = defaultdict(lambda: defaultdict(int))
    for group, user_ids in group_users_map.items():
        user_ids.add(0)     # 获取所有用户数据
        user_main_dic = get_sub_account_mapping(user_ids)
        params = [dict(user_id=user_id, group=group) for user_id in user_ids]
        data_mapper = g_map(get_req_trading_data, params, fail_safe={}, ordered=True, size=20)
        for data_ in data_mapper:
            user_id = data_['user_id']
            req_data = data_['data']
            for v in req_data:
                ts, count = v[0], v[1]
                if ts < start_ts or ts >= end_ts:
                    continue
                current_minute = convert_datetime(timestamp_to_datetime(ts), "minute")
                main_user_id = user_main_dic.get(user_id, user_id)
                key = f"{group.name}:{main_user_id}"
                time_data_mapper[current_minute][key] += count

    res = []
    for time, group_data in time_data_mapper.items():
        for key, count in group_data.items():
            if count == 0:
                continue
            group, user_id = str(key).split(":")
            row = get_or_create_by_model(ApiRequest, time, group, user_id)
            row.req_count = count
            res.append(row)

    db.session.add_all(res)
    db.session.commit()


def get_req_trading_data(param_dict: Dict):
    group, user_id = param_dict['group'], param_dict['user_id']
    client = ApiGatewayClient()
    data = client.req_trending(group, user_id)
    return dict(
        user_id=user_id,
        data=data,
    )


@scheduled(crontab(minute="*/20"), queue=CeleryQueues.STATISTIC)
@lock_call()
def flush_hour_api_requests():
    now_ = now()
    end_time = convert_datetime(now_, "hour")
    start_time = end_time - datetime.timedelta(days=1)
    last_data = HourApiRequest.query.order_by(HourApiRequest.time.desc()).first()
    if last_data:
        start_time = last_data.time + datetime.timedelta(hours=1)
    if start_time >= end_time:
        return

    time_data_mapper = defaultdict(lambda: defaultdict(int))
    for row in ApiRequest.query.filter(ApiRequest.time >= start_time, ApiRequest.time < end_time):
        current_hour = convert_datetime(row.time, "hour")
        key = f"{row.group}:{row.user_id}"
        time_data_mapper[current_hour][key] += row.req_count

    res = []
    for time, group_data in time_data_mapper.items():
        for key, req_count in group_data.items():
            group, user_id = str(key).split(":")
            hr = get_or_create_by_model(HourApiRequest, time, group, user_id)
            hr.req_count = req_count
            res.append(hr)
    db.session.add_all(res)
    db.session.commit()


@scheduled(crontab(hour="*/1", minute="3"), queue=CeleryQueues.STATISTIC)
@lock_call()
def flush_daily_api_requests():
    now_ = now()
    end_time = convert_datetime(now_, "day")
    start_time = end_time - datetime.timedelta(days=1)
    last_data = DailyApiRequest.query.order_by(DailyApiRequest.time.desc()).first()
    if last_data:
        start_time = last_data.time + datetime.timedelta(days=1)
    if start_time >= end_time:
        return

    time_data_mapper = defaultdict(lambda: defaultdict(int))
    for row in HourApiRequest.query.filter(HourApiRequest.time >= start_time, HourApiRequest.time < end_time):
        current_day = convert_datetime(row.time, "day")
        key = f"{row.group}:{row.user_id}"
        time_data_mapper[current_day][key] += row.req_count

    res = []
    for time, group_data in time_data_mapper.items():
        for key, req_count in group_data.items():
            group, user_id = str(key).split(":")
            dr = get_or_create_by_model(DailyApiRequest, time, group, user_id)
            dr.req_count = req_count
            res.append(dr)

    db.session.add_all(res)
    db.session.commit()


def get_sub_account_mapping(user_ids):
    q = SubAccount.query.filter(
        SubAccount.user_id.in_(user_ids)
    ).with_entities(
        SubAccount.main_user_id, SubAccount.user_id
    ).all()
    return {v.user_id: v.main_user_id for v in q}


@scheduled(crontab(minute="4", hour="1"))
def clear_api_requests_data():
    now_ = now()
    ApiRequest.query.filter(
        ApiRequest.time < now_ - datetime.timedelta(days=7)
    ).delete()
    HourApiRequest.query.filter(
        HourApiRequest.time < now_ - datetime.timedelta(days=90)
    ).delete()
    db.session.commit()