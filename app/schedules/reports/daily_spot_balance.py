import datetime

from flask import current_app

from app.assets import list_all_assets
from app.business import db, \
    route_module_to_celery_queue, CeleryQueues, scheduled, crontab, TradeLogDB, lock_call
from app.models import DailySpotBalanceReport
from app.utils.date_ import datetime_to_time

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def update_daily_spot_report_date(date):
    table_timestamp = datetime_to_time(date) + 86400
    slice_timestamp = TradeLogDB.get_slice_history_timestamp(table_timestamp)
    if not slice_timestamp:
        return
    log_db = TradeLogDB.db
    cursor = log_db.cursor()

    sql = """SELECT SUM(balance) AS amount, 
             SUM(CASE WHEN (balance >= 0 and balance < {level1}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level1} and balance < {level2}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level2} and balance < {level3}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level3} and balance < {level4}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level4} and balance < {level5}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level5} and balance < {level6}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level6} and balance < {level7}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level7} and balance < {level8}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level8} and balance < {level9}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level9}) THEN balance ELSE 0 END), 
             SUM(CASE WHEN (balance >= 0 and balance < {level1}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level1} and balance < {level2}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level2} and balance < {level3}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level3} and balance < {level4}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level4} and balance < {level5}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level5} and balance < {level6}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level6} and balance < {level7}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level7} and balance < {level8}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level8} and balance < {level9}) THEN 1 ELSE 0 END), 
             SUM(CASE WHEN (balance >= {level9}) THEN 1 ELSE 0 END), 
             asset
             FROM slice_balance_{tag} GROUP BY asset """.format(
        level1=DailySpotBalanceReport.LEVEL_TABLE['LEVEL1'],
        level2=DailySpotBalanceReport.LEVEL_TABLE['LEVEL2'],
        level3=DailySpotBalanceReport.LEVEL_TABLE['LEVEL3'],
        level4=DailySpotBalanceReport.LEVEL_TABLE['LEVEL4'],
        level5=DailySpotBalanceReport.LEVEL_TABLE['LEVEL5'],
        level6=DailySpotBalanceReport.LEVEL_TABLE['LEVEL6'],
        level7=DailySpotBalanceReport.LEVEL_TABLE['LEVEL7'],
        level8=DailySpotBalanceReport.LEVEL_TABLE['LEVEL8'],
        level9=DailySpotBalanceReport.LEVEL_TABLE['LEVEL9'],
        tag=slice_timestamp)

    cursor.execute(sql)
    items = cursor.fetchall()
    has_data_set = []
    for *item, asset in items:
        record = DailySpotBalanceReport.get_or_create(
            report_date=date,
            asset=asset
        )
        (
            record.amount,
            record.amount_0,
            record.amount_1,
            record.amount_2,
            record.amount_3,
            record.amount_4,
            record.amount_5,
            record.amount_6,
            record.amount_7,
            record.amount_8,
            record.amount_9,
            record.count_0,
            record.count_1,
            record.count_2,
            record.count_3,
            record.count_4,
            record.count_5,
            record.count_6,
            record.count_7,
            record.count_8,
            record.count_9
        ) = item

        has_data_set.append(asset)

        db.session.add(record)

    for asset in list_all_assets():
        if asset not in has_data_set:
            record = DailySpotBalanceReport.get_or_create(
                report_date=date,
                asset=asset
            )
            record.amount = 0.00
            db.session.add(record)

    try:
        db.session.commit()
    except Exception as e:
        print(str(e))
        db.session.rollback()


@scheduled(crontab(minute='5,25,45', hour=0))
@lock_call()
def update_daily_balance_report_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailySpotBalanceReport.query.order_by(
        DailySpotBalanceReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date - datetime.timedelta(days=1)
    else:
        start_date = today + datetime.timedelta(days=-30)
    while start_date < today:

        try:
            update_daily_spot_report_date(start_date)
        except Exception as ex:
            import traceback
            trace_info = traceback.format_exc()
            msg = 'update_daily_balance_report_schedule {start_date} exception, ex: {ex}, trace_info: {trace_info}'.format(
                start_date=start_date,
                ex=str(ex),
                trace_info=trace_info
            )
            current_app.logger.error(msg)
        start_date += datetime.timedelta(days=1)
