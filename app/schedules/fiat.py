#!/usr/bin/env python3

from collections import defaultdict
import datetime
import json
from itertools import product
from decimal import Decimal

from celery.schedules import crontab
from flask import current_app
from app.business.fiat.btcdirect import BTCDirectClient
from app.common.constants import PrecisionEnum
from app.models.user import SubAccount, User
from app.utils.amount import quantize_amount
from app.utils.date_ import current_timestamp, datetime_to_time

from app.utils.http_client import RESTClient
from app.utils.iterable import batch_iter
from .. import config

from ..business import lock_call
from ..business.utils import query_records_by_time_range
from ..business.alert import send_alert_notice
from ..business.fiat.banxa import BanxaClient
from ..business.fiat.guardarian import GuardarianClient
from ..business.fiat.onramp import OnRampClient
from ..business.fiat.remitano import RemitanoClient
from ..common import CeleryQueues
from ..business.fiat import MercuryoClient, SimplexClient, MoonPayClient, VoletClient, get_fiat_partners, \
    get_fiat_partner_client, LoopipayClient, AlchemyPayClient
from ..business.fiat.base import SupportType
from ..caches.fiat import FiatPartnerOfflineMemoCache, FiatPriceCache, FiatActivityCache, FiatPartnerMemoCache, FiatPriceDeviationAlertCache
from ..common import ProducerTopics
from ..models import db
from ..models.fiat import FiatOrder
from ..producer import exactly_once_producer
from ..utils import scheduled, now, celery_task, route_module_to_celery_queue
from ..utils.parser import JsonEncoder

route_module_to_celery_queue(__name__, CeleryQueues.REAL_TIME)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_fiat_partner_price_caches_schedule():
    for partner in get_fiat_partners():
        update_fiat_price_cache_task.delay(partner)


@celery_task
@lock_call(with_args=True)
def update_fiat_price_cache_task(partner_name):
    client = get_fiat_partner_client(partner_name)
    try:
        api_data = client.get_prices()
    except NotImplementedError:
        return
    cache = FiatPriceCache(partner_name)
    partner_current_keys = []
    for support_type in SupportType:
        if support_type not in client.support_types:
            continue
        assets = getattr(client, f"{support_type.value}_assets")
        fiats = getattr(client, f"{support_type.value}_fiats")
        partner_current_keys += [f"{support_type.value}-{asset}-{fiat}" for asset, fiat in product(assets, fiats)]
    cache_keys = set(cache.hkeys())
    if remove_keys := cache_keys - set(partner_current_keys):
        cache.hdel(*remove_keys)
    if api_data:
        cache.hmset(api_data)


def poll_sell_simplex_msgs(client: SimplexClient):
    status_mapper = {
        "txn-payout": FiatOrder.StatusType.APPROVED,
        'txn-declined': FiatOrder.StatusType.DECLINED,
        'txn-refunded': FiatOrder.StatusType.REFUNDED
    }
    messages = client.get_msgs()
    order_status_dict = {}
    for message in messages["messages"]:
        msg_type = message['msg_type']
        msg_id = message['msg_id']
        msg = message['msg']
        # TODO other msg type keep it now.
        if msg_type != 'txn-event-notify':
            continue
        order_status_dict[msg['txn_id']] = {
            "msg_id": msg_id,
            "event": msg['event'],
            "timestamp": msg['timestamp']
        }
    records = FiatOrder.list_record_by_payment_id(order_status_dict.keys())
    for record in records:  # type: FiatOrder
        payment_id = record.payment_id
        msg = order_status_dict[payment_id]
        status = status_mapper[msg['event']]
        if status == FiatOrder.StatusType.APPROVED:
            record.approved_at = now()
        record.status = status
        record.event = json.dumps(msg, cls=JsonEncoder)
        db.session.commit()
        client.ack_receipt(msg['msg_id'])
        if record.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(record)


def _send_fiat_order_event_msg(fiat_order: FiatOrder):
    if fiat_order.order_type != FiatOrder.OrderType.BUY:
        return
    message = dict(
        event_data=dict(
            amount=fiat_order.coin_amount,
            amount_asset=fiat_order.asset,
            order_type="fiat_order"
        ),
        biz_type=FiatOrder.__tablename__,
        biz_id=fiat_order.id,
        timestamp=datetime_to_time(fiat_order.created_at),
        user_id=fiat_order.user_id
    )
    exactly_once_producer.send_message(ProducerTopics.DEPOSIT_REWARD, message)


@scheduled(crontab(minute="*/5"))
@lock_call()
def poll_simplex_events_schedule():
    status_map = {
        'payment_simplexcc_declined': FiatOrder.StatusType.DECLINED,
        'payment_request_submitted': FiatOrder.StatusType.PENDING,
        'payment_simplexcc_approved': FiatOrder.StatusType.PENDING,  # payment is approved but crypto not delivered yet
        'payment_simplexcc_refunded': FiatOrder.StatusType.REFUNDED,
        'payment_cancelled': FiatOrder.StatusType.DECLINED,
        'payment_simplexcc_crypto_sent': FiatOrder.StatusType.APPROVED
    }
    client = SimplexClient()
    events = client.get_events()
    payment_ids = [item['payment']['id'] for item in events['events']]

    records = FiatOrder.list_record_by_payment_id(payment_ids)

    payments_hash = {r.payment_id: r for r in records}

    for event in events['events']:
        payment_id = event['payment']['id']

        if payment_id not in payments_hash:
            client.delete_event(event['event_id'])
            continue

        record = payments_hash[payment_id]
        status = status_map[event['name']]
        record.status = status
        if status == FiatOrder.StatusType.APPROVED:
            record.approved_at = now()
            chain, tx_id, tx_url = client.get_tx_info(event.get("payment", {}), record.asset, record.order_type)
            record.chain, record.tx_id, record.tx_url = chain, tx_id, tx_url
            if coin_amount := event.get("payment", {}).get("crypto_total_amount", {}).get("amount"):
                record.coin_amount = coin_amount
        record.event = json.dumps(event, cls=JsonEncoder)
        db.session.commit()
        client.delete_event(event['event_id'])
        if record.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(record)


def _get_pending_orders(third_party: str):
    end_time = now()
    start_time = end_time - datetime.timedelta(days=15)
    query = query_records_by_time_range(FiatOrder,
                                        start_time=start_time,
                                        end_time=end_time,
                                        filters={'third_party': third_party},
                                        limit=200)
    orders = []
    for row in query:
        if row.status == FiatOrder.StatusType.CREATE or row.status == FiatOrder.StatusType.PENDING:
            orders.append(row)
    return orders


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_mercuryo_orders_schedule():
    status_map = {
        'new': FiatOrder.StatusType.CREATE,
        'pending': FiatOrder.StatusType.PENDING,
        'cancelled': FiatOrder.StatusType.DECLINED,
        'paid': FiatOrder.StatusType.APPROVED,
        'order_failed': FiatOrder.StatusType.DECLINED,
        'order_scheduled': FiatOrder.StatusType.PENDING,
        'descriptor_failed': FiatOrder.StatusType.DECLINED,
        'failed_exchange': FiatOrder.StatusType.DECLINED,
        'succeeded': FiatOrder.StatusType.APPROVED,
        'failed': FiatOrder.StatusType.DECLINED,
        # 兼容服务商 异常数据 正常应该是 paid
        'completed': FiatOrder.StatusType.APPROVED
    }
    end_time = now() - datetime.timedelta(hours=6)
    client = MercuryoClient()
    orders = _get_pending_orders(client.name)
    for order in orders:
        r = client.get_order(order.order_id, order.order_type)
        if r:
            status = status_map.get(r['status'])
            if not status or status == order.status:
                continue
            order.coin_amount = r['amount']
            order.fiat_total_amount = r['fiat_amount']
            order.event = json.dumps(r, cls=JsonEncoder)
            order.status = status
            if status == FiatOrder.StatusType.APPROVED:
                order.approved_at = now()
                chain, tx_id, tx_url = client.get_tx_info(order.order_id, order.asset, order.order_type)
                order.chain, order.tx_id, order.tx_url = chain, tx_id, tx_url
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_moonpay_orders_schedule():
    status_map = {
        # BUY
        'waitingPayment': FiatOrder.StatusType.PENDING,
        'waitingAuthorization': FiatOrder.StatusType.PENDING,
        # SELL
        'waitingForDeposit': FiatOrder.StatusType.PENDING,

        # Common
        'pending': FiatOrder.StatusType.PENDING,
        'failed': FiatOrder.StatusType.DECLINED,
        'completed': FiatOrder.StatusType.APPROVED,
    }
    end_time = now() - datetime.timedelta(hours=6)
    client = MoonPayClient()
    orders = _get_pending_orders(client.name)
    for order in orders:
        r = client.get_order(order.order_id, order.order_type)
        if r:
            status = status_map[r['status']]
            if status != order.status:
                if order.order_type is FiatOrder.OrderType.BUY:
                    if coin_amount := r['quoteCurrencyAmount']:
                        order.coin_amount = coin_amount
                    if fiat_amount := r['baseCurrencyAmount']:
                        order.fiat_total_amount = fiat_amount
                else:
                    if coin_amount := r['baseCurrencyAmount']:  # TODO: to be updated
                        order.coin_amount = coin_amount
                    if fiat_amount := r['quoteCurrencyAmount']:
                        order.fiat_total_amount = fiat_amount
                order.event = json.dumps(r, cls=JsonEncoder)
                order.status = status
                if status == FiatOrder.StatusType.APPROVED:
                    order.approved_at = now()
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_volet_orders_schedule():
    status_map = {
        'NEW': FiatOrder.StatusType.CREATE,
        'PROCESSING': FiatOrder.StatusType.PENDING,
        'COMPLETED': FiatOrder.StatusType.APPROVED,
        'EXPIRED': FiatOrder.StatusType.DECLINED
    }
    end_time = now() - datetime.timedelta(hours=6)
    client = VoletClient()
    orders = _get_pending_orders(client.name)
    for order in orders:
        r = client.get_order(order.order_id)
        if r:
            status = status_map[r['status']]
            if status != order.status:
                if coin_amount := r.get('cryptoCurrencyAmount'):
                    order.coin_amount = coin_amount
                if fiat_amount := r.get('fiatAmount'):
                    order.fiat_total_amount = fiat_amount
                order.event = json.dumps(r, cls=JsonEncoder)
                order.status = status
                if status == FiatOrder.StatusType.APPROVED:
                    order.approved_at = now()
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_banxa_orders_schedule():
    status_mapping = {
        "pendingPayment":  FiatOrder.StatusType.CREATE,
        "eddRequired":  FiatOrder.StatusType.PENDING,
        "waitingPayment": FiatOrder.StatusType.PENDING,
        "paymentReceived": FiatOrder.StatusType.PENDING,
        "inProgress": FiatOrder.StatusType.PENDING,
        "coinTransferred": FiatOrder.StatusType.PENDING,
        "extraVerification": FiatOrder.StatusType.PENDING,
        "cancelled": FiatOrder.StatusType.DECLINED,
        "declined": FiatOrder.StatusType.DECLINED,
        "expired": FiatOrder.StatusType.DECLINED,
        "complete": FiatOrder.StatusType.APPROVED,
        "refunded": FiatOrder.StatusType.REFUNDED
    }

    end_time = now() - datetime.timedelta(hours=6)
    client = BanxaClient()
    orders = _get_pending_orders(client.name)
    for order in orders:  # type: FiatOrder
        r = client.get_order(order.payment_id)
        if r:
            status = status_mapping.get(r['status'])
            if not status:
                current_app.logger.warning(f'banxa order_id: {order.id} unknown status: {r["status"]}')
                continue
            if order.status != status:
                order.status = status
            if coin_amount := r.get('coin_amount'):
                order.coin_amount = coin_amount
            if fiat_amount := r.get("fiat_amount"):
                order.fiat_total_amount = fiat_amount
            # 该服务没办法限制用户修改 法币
            if fiat_code := r.get("fiat_code"):
                order.fiat_currency = fiat_code
            order.event = json.dumps(r, cls=JsonEncoder)
            if status == FiatOrder.StatusType.APPROVED:
                order.approved_at = now()
                chain, tx_id, tx_url = client.get_tx_info(r, order.asset, order.order_type, order.user_id)
                order.chain, order.tx_id, order.tx_url = chain, tx_id, tx_url
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_guardarian_orders_schedule():
    status_map = {
        'new': FiatOrder.StatusType.CREATE,
        'finished': FiatOrder.StatusType.APPROVED,
        'refunded': FiatOrder.StatusType.REFUNDED,
        'waiting_for_customer': FiatOrder.StatusType.PENDING,
        'waiting_for_deposit': FiatOrder.StatusType.PENDING,
        'exchanging': FiatOrder.StatusType.PENDING,
        'on_hold': FiatOrder.StatusType.PENDING,
        'sending': FiatOrder.StatusType.PENDING,
        'cancelled': FiatOrder.StatusType.DECLINED,
        'failed': FiatOrder.StatusType.DECLINED,
        'expired': FiatOrder.StatusType.DECLINED
    }
    end_time = now() - datetime.timedelta(hours=6)
    client = GuardarianClient()
    orders = _get_pending_orders(client.name)
    for order in orders:
        try:
            r = client.get_order(order.order_id)
        except Exception as e:
            current_app.logger.warning(f'order_id: {order.order_id} \n'
                                       f' headers: {client._client.headers()} \n'
                                       f' error: {e!r}')
            continue
        if r:
            status = status_map[r['status']]
            if status != order.status:
                if order.order_type is FiatOrder.OrderType.BUY:
                    if coin_amount := r['expected_to_amount']:
                        order.coin_amount = coin_amount
                    if fiat_amount := r['expected_from_amount']:
                        order.fiat_total_amount = fiat_amount
                else:
                    if coin_amount := r['expected_from_amount']:
                        order.coin_amount = coin_amount
                    if fiat_amount := r['expected_to_amount']:
                        order.fiat_total_amount = fiat_amount
                order.event = json.dumps(r, cls=JsonEncoder)
                order.status = status
                if status == FiatOrder.StatusType.APPROVED:
                    order.approved_at = now()
                    chain, tx_id, tx_url = client.get_tx_info(r, order.asset, order.order_type)
                    order.chain, order.tx_id, order.tx_url = chain, tx_id, tx_url
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


# @scheduled(crontab(minute="*/5"))
# @lock_call()
def update_loopipay_orders_schedule():
    status_map = {
        'NOT_PAID': FiatOrder.StatusType.DECLINED,
        'PAID': FiatOrder.StatusType.PENDING,
        'PAYMENT_PENDING': FiatOrder.StatusType.CREATE,
        'TRANSACTION_FAILED': FiatOrder.StatusType.DECLINED,
        'TRANSACTION_SUCCESS': FiatOrder.StatusType.APPROVED,
        'TRANSACTION_PENDING': FiatOrder.StatusType.PENDING,
        'TRANSACTION_MINED': FiatOrder.StatusType.PENDING,
        'TRANSACTION_SENT': FiatOrder.StatusType.PENDING,
        'CANCELLED': FiatOrder.StatusType.DECLINED,
        'REFUNDED': FiatOrder.StatusType.REFUNDED,
        
        'CREATED': FiatOrder.StatusType.CREATE,
        'FAILED': FiatOrder.StatusType.DECLINED,
        'TRANSFER_SENT': FiatOrder.StatusType.PENDING,
        'TRANSFER_SUCCESS': FiatOrder.StatusType.APPROVED,
        'TRANSFER_FAILED': FiatOrder.StatusType.DECLINED,
    }
    end_time = now() - datetime.timedelta(hours=6)
    client = LoopipayClient()
    orders = _get_pending_orders(client.name)
    for order in orders:
        try:
            r = client.get_order(order.order_id)
        except Exception as e:
            if isinstance(e, RESTClient.BadResponse) and e.code == 404:
                r = None
            else:
                current_app.logger.warning(f'order_id: {order.order_id} \n'
                                        f' headers: {client._client.headers()} \n'
                                        f' error: {e!r}')
                continue
        if r:
            status = status_map[r['status']]
            if status != order.status:
                if fiat_info := r.get('fiat'):
                    order.fiat_total_amount = \
                        Decimal(fiat_info['quantity']).scaleb(-int(fiat_info['decimals']))
                if token_info := r.get('token'):
                    order.coin_amount = \
                        Decimal(token_info['quantity']).scaleb(-int(token_info['decimals']))
                order.event = json.dumps(r, cls=JsonEncoder)
                order.status = status
                if status == FiatOrder.StatusType.APPROVED:
                    order.approved_at = now()
                    chain, tx_id, tx_url = client.get_tx_info(r, order.asset, order.order_type)
                    order.chain, order.tx_id, order.tx_url = chain, tx_id, tx_url
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_remitano_orders_schedule():
    status_map = {
        'pending': FiatOrder.StatusType.CREATE,
        'confirmed': FiatOrder.StatusType.PENDING,
        'pre_processing': FiatOrder.StatusType.PENDING,
        'processing': FiatOrder.StatusType.PENDING,
        'completed': FiatOrder.StatusType.APPROVED,
        'cancelled': FiatOrder.StatusType.DECLINED,
    }
    end_time = now() - datetime.timedelta(hours=6)
    client = RemitanoClient()
    orders = _get_pending_orders(client.name)
    for order in orders:
        r = client.get_order(order.order_id)
        if r:
            status = status_map[r['status']]
            if status != order.status:
                if coin_amount := r['coin_amount']:
                    order.coin_amount = coin_amount
                if fiat_amount := r['fiat_amount']:
                    order.fiat_total_amount = fiat_amount
                order.event = json.dumps(r, cls=JsonEncoder)
                order.status = status
                if status == FiatOrder.StatusType.APPROVED:
                    order.approved_at = now()
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_onramp_orders_schedule():
    status_map = {
        -4: FiatOrder.StatusType.DECLINED,
        -3: FiatOrder.StatusType.DECLINED,
        -2: FiatOrder.StatusType.DECLINED,
        -1: FiatOrder.StatusType.DECLINED,
        0: FiatOrder.StatusType.PENDING,
        1: FiatOrder.StatusType.PENDING,
        2: FiatOrder.StatusType.PENDING,
        3: FiatOrder.StatusType.PENDING,
        4: FiatOrder.StatusType.APPROVED,
        5: FiatOrder.StatusType.APPROVED,
        11: FiatOrder.StatusType.PENDING,
        12: FiatOrder.StatusType.PENDING,
        13: FiatOrder.StatusType.PENDING,
        14: FiatOrder.StatusType.PENDING,
        15: FiatOrder.StatusType.APPROVED,
        16: FiatOrder.StatusType.APPROVED,
    }
    end_time = now() - datetime.timedelta(hours=6)
    client = OnRampClient()
    orders = _get_pending_orders(client.name)
    if not orders:
        return
    min_created_time = min([i.created_at for i in orders])
    fiat_order_list = client.get_order_list(min_created_time)
    fiat_order_map = {
        i["merchantRecognitionId"]: i for i in fiat_order_list
    }
    for order in orders:
        if r := fiat_order_map.get(order.order_id):
            status = status_map.get(r['status'])
            if status is not None and status != order.status:
                if order.order_type is FiatOrder.OrderType.BUY:
                    if coin_amount := r.get('actualCryptoAmount'):
                        order.coin_amount = coin_amount
                    if fiat_amount := r.get('fiatAmount'):
                        order.fiat_total_amount = fiat_amount
                else:
                    if coin_amount := r.get('actualQuantity'):
                        order.coin_amount = coin_amount
                    if fiat_amount := r.get('actualPrice'):
                        order.fiat_total_amount = fiat_amount
                order.event = json.dumps(r, cls=JsonEncoder)
                order.status = status
                if status == FiatOrder.StatusType.APPROVED:
                    order.approved_at = now()
                    chain, tx_id, tx_url = client.get_tx_info(r, order.asset, order.order_type)
                    order.chain, order.tx_id, order.tx_url = chain, tx_id, tx_url
        elif order.created_at < end_time:  # 超时
            order.status = FiatOrder.StatusType.DECLINED
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_btcdirect_orders_schedule():
    status_map = {
        'cancelled': FiatOrder.StatusType.DECLINED,
        'pending': FiatOrder.StatusType.PENDING,
        'in progress': FiatOrder.StatusType.PENDING,
        'blocked': FiatOrder.StatusType.PENDING,
        'completed': FiatOrder.StatusType.APPROVED,
        'refunded': FiatOrder.StatusType.REFUNDED,
    }
    client = BTCDirectClient()
    orders = _get_pending_orders(client.name)
    user_ids = {order.user_id for order in orders}
    sub_user_map = dict()
    for ids in batch_iter(user_ids, 1000):
        tmp = SubAccount.query.filter(
            SubAccount.user_id.in_(ids),
        ).with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        sub_user_map.update(dict(tmp))
    main_user_ids = set()
    for user_id in user_ids:
        main_user_ids.add(sub_user_map.get(user_id, user_id))
    user_email_map = dict()
    for ids in batch_iter(main_user_ids, 1000):
        tmp = User.query.filter(
            User.id.in_(ids),
        ).with_entities(User.id, User.email).all()
        user_email_map.update(dict(tmp))
    for order in orders:
        if order.created_at < now() - datetime.timedelta(days=7):
            order.status = FiatOrder.StatusType.DECLINED
            continue
        email = user_email_map[order.user_id]
        try:
            r = client.get_order(order.order_id, email, order.order_type.name)
        except Exception as e:
            current_app.logger.warning(f'order_id: {order.order_id} \n'
                                       f' headers: {client._client.headers()} \n'
                                       f' error: {e!r}')
            continue
        status = status_map[r['status']]
        order.status = status
        if status == FiatOrder.StatusType.APPROVED:
            order.approved_at = now()
            chain, tx_id, tx_url = client.get_tx_info(r, order.asset, order.order_type)
            order.chain, order.tx_id, order.tx_url = chain, tx_id, tx_url
        order.deposit_address = r['blockchainInfo']['walletAddress']
        asset, fiat = r['currencyPair'].split('-')
        order.asset = asset
        order.fiat_currency = fiat
        coin_amount, fiat_amount = Decimal(r['value']['amount']), Decimal(r['price']['amount'])
        order.coin_amount = quantize_amount(coin_amount, PrecisionEnum.COIN_PLACES)
        order.fiat_total_amount = quantize_amount(fiat_amount, 
                                                  PrecisionEnum.PRICE_PLACES)
        order.event = json.dumps(r, cls=JsonEncoder)
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_alchemypay_orders_schedule():
    buy_status_map = {
        'PENDING': FiatOrder.StatusType.PENDING,
        'TRANSFER': FiatOrder.StatusType.PENDING,
        'CANCEL': FiatOrder.StatusType.DECLINED,
        'PAY_FAIL': FiatOrder.StatusType.DECLINED,
        'PAY_SUCCESS': FiatOrder.StatusType.PENDING,
        'FINISHED': FiatOrder.StatusType.APPROVED,
        'RISK_CONTROL': FiatOrder.StatusType.PENDING,
        'REFUNDED': FiatOrder.StatusType.REFUNDED,
        'INVALID_ADDRESS': FiatOrder.StatusType.PENDING,
    }
    sell_status_map = {
        '1': FiatOrder.StatusType.PENDING,
        '2': FiatOrder.StatusType.PENDING,
        '3': FiatOrder.StatusType.PENDING,
        '4': FiatOrder.StatusType.PENDING,
        '5': FiatOrder.StatusType.DECLINED,
        '6': FiatOrder.StatusType.APPROVED,
        '7': FiatOrder.StatusType.DECLINED,
    }
    client = AlchemyPayClient()
    orders = _get_pending_orders(client.name)
    for order in orders:
        if order.created_at < now() - datetime.timedelta(days=7):
            order.status = FiatOrder.StatusType.DECLINED
            continue
        try:
            r = client.get_order(order.order_id, order.order_type)
        except Exception as e:
            current_app.logger.warning(f'order_id: {order.order_id} \n'
                                       f' headers: {client._client.headers()} \n'
                                       f' error: {e!r}')
            continue
        if not r:
            continue
        if order.order_type is FiatOrder.OrderType.BUY:
            if coin_amount := r.get('cryptoQuantity'):
                order.coin_amount = coin_amount
            if fiat_amount := r.get('amount'):
                order.fiat_total_amount = fiat_amount
        else:
            if coin_amount := r.get('cryptoAmount'):
                order.coin_amount = coin_amount
            if fiat_amount := r.get('fiatAmount'):
                order.fiat_total_amount = fiat_amount
        status_map = buy_status_map if order.order_type is FiatOrder.OrderType.BUY else sell_status_map
        status = status_map[r['status']]
        order.status = status
        if status == FiatOrder.StatusType.APPROVED:
            order.approved_at = now()
            # do not have tx_url
            order.chain, order.tx_id = client.get_asset_chain(order.asset), r['txHash']
        order.event = json.dumps(r, cls=JsonEncoder)
        db.session.commit()
        if order.status == FiatOrder.StatusType.APPROVED:
            _send_fiat_order_event_msg(order)


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_fiat_activity_cache_schedule():
    FiatActivityCache.reload()


@scheduled(crontab(day_of_week=1, hour=2, minute=30))
@lock_call()
def send_fiat_update_notice_schedule():
    if not config.get('MONITOR_ENABLED'):
        return
    for partner_name in get_fiat_partners():
        update_fiat_update_notice_task.delay(partner_name)


@celery_task
@lock_call(with_args=True)
def update_fiat_update_notice_task(partner_name):
    url = config["ADMIN_CONTACTS"]['fiat_notice']
    if not url:
        return

    def _get_fiats_and_assets_from(client):
        try:
            _fiats = client.get_supported_fiats()
        except NotImplementedError:
            _fiats = []

        try:
            _assets = client.get_supported_assets()
        except NotImplementedError:
            _assets = []
        return _fiats, _assets

    c = get_fiat_partner_client(partner_name)
    fiats, assets = _get_fiats_and_assets_from(client=c)
    fixed_fiats, fixed_assets = getattr(c, 'buy_fiats', []) + getattr(c, 'sell_fiats', []), \
                                getattr(c, 'buy_assets', []) + getattr(c, 'sell_assets', [])
    cache = FiatPartnerMemoCache(partner_name)
    cache_ret = cache.read_aside()
    pushed_fiats, pushed_assets = cache_ret.get('fiats', []), cache_ret.get('assets', [])
    to_save_fiats = list(set(fiats) | set(fixed_fiats) | set(pushed_fiats))
    to_save_assets = list(set(assets) | set(fixed_assets) | set(pushed_assets))
    to_save_map = {
        'fiats': to_save_fiats,
        'assets': to_save_assets,
    }

    fiats_to_be_pushed = list(set(fiats) - set(fixed_fiats) - set(pushed_fiats))
    assets_to_be_pushed = list(set(assets) - set(fixed_assets) - set(pushed_assets))
    content = ''
    human = '<@U06QNMUV807> <@U09L80UL5C4>'
    if assets_to_be_pushed:
        content += f'{partner_name} 服务商入金或出金有新增的数字货币: \n>'
        content += f' {", ".join(assets_to_be_pushed)}.\n>'
        content += f' 请及时处理 {human}\n'
    if fiats_to_be_pushed:
        content += f'{partner_name} 服务商入金或出金有新增法币: \n>'
        content += f' {", ".join(fiats_to_be_pushed)}.\n>'
        content += f' 请及时处理 {human}\n'
    if content:
        send_alert_notice(
            content=content,
            url=url,
        )
    cache.set(json.dumps(to_save_map, cls=JsonEncoder))

    # 下架币提醒
    cache = FiatPartnerOfflineMemoCache(partner_name)
    cache_ret = cache.read_aside()
    pushed_fiats, pushed_assets = cache_ret.get('fiats', []), cache_ret.get('assets', [])
    fixed_fiats = set(fixed_fiats)
    fiats = set(fiats)
    pushed_fiats = set(pushed_fiats)
    to_save_fiats = list(fixed_fiats - fiats | pushed_fiats)

    fixed_assets = set(fixed_assets)
    assets = set(assets)
    pushed_assets = set(pushed_assets)
    to_save_assets =  list(fixed_assets - assets | pushed_assets)
    to_save_map = {
        'fiats': to_save_fiats,
        'assets': to_save_assets,
    }
    fiats_to_be_pushed = list(fixed_fiats - fiats - pushed_fiats)
    assets_to_be_pushed = list(fixed_assets - assets - pushed_assets)

    if fiats_to_be_pushed:
        content = f'{partner_name} 服务商入金或出金有下架法币: \n>'
        content += f' {", ".join(fiats_to_be_pushed)}.\n>'
        content += f' 请及时处理 {human}\n'
        send_alert_notice(
            content=content,
            url=url,
        )
    if assets_to_be_pushed:
        content = f'{partner_name} 服务商入金或出金有下架的数字货币: \n>'
        content += f' {", ".join(assets_to_be_pushed)}.\n>'
        content += f' 请及时处理 {human}\n'
        send_alert_notice(
            content=content,
            url=url,
        )
    cache.set(json.dumps(to_save_map, cls=JsonEncoder))


@scheduled(crontab(day_of_week=1, minute='*/20', hour=2))
@lock_call()
def send_partners_asset_price_deviation_schedule(force_send=False):
    """
    服务商币价偏差监控告警
    """
    if not config.get('MONITOR_ENABLED'):
        return
    THRESHOLD = Decimal('0.2')
    CONTACTS = '<@U06QNMUV807> <@U03BUSKD988> <@U09L80UL5C4>'
    MONITORED_ASSETS = ('USDT', 'USDC', 'BTC', 'ETH')

    partner_buy_price_map = defaultdict(lambda: defaultdict(Decimal))
    partner_sell_price_map = defaultdict(lambda: defaultdict(Decimal))
    for name in get_fiat_partners():
        cache = FiatPriceCache(name)
        name = name.lower()
        all_price_map = cache.get_all_prices()
        for k, v in all_price_map[SupportType.BUY].items():
            partner_buy_price_map[k][name] = v
        for k, v in all_price_map[SupportType.SELL].items():
            partner_sell_price_map[k][name] = v

    price_maps = {
        SupportType.BUY: partner_buy_price_map,
        SupportType.SELL: partner_sell_price_map,
    }
    # MoonPay 价格特殊处理(考虑费率)
    client = get_fiat_partner_client('moonpay')
    for support_type in SupportType:
        assets = getattr(client, f"{support_type.name.lower()}_assets")
        fiats = getattr(client, f"{support_type.name.lower()}_fiats")
        for asset, fiat in product(assets, fiats):
            price = client.asset_to_fiat(asset, fiat, support_type)
            if price <= 0:
                continue
            price_maps[support_type][(asset, fiat)][client.name.lower()] = price

    contents = []
    for support_type, partner_price_map in price_maps.items():
        for k, v in partner_price_map.items():
            asset, fiat = k
            if asset not in MONITORED_ASSETS:
                continue
            for name, price in v.items():
                if len(v) == 1 or price <= 0:
                    continue
                if support_type == SupportType.BUY:
                    direction = '入金'
                else:
                    direction = '出金'
                average_price = sum(v for k, v in v.items() if k != name) / (len(v) - 1)
                average_price = quantize_amount(average_price, PrecisionEnum.CASH_PLACES)
                price = quantize_amount(price, PrecisionEnum.CASH_PLACES)
                if abs(price - average_price) / price >= THRESHOLD:
                    alert_cache = FiatPriceDeviationAlertCache(name, support_type.name, asset, fiat)
                    if alert_cache.exists() and not force_send:
                        continue
                    if price > average_price:
                        content = f'服务商 {name} {asset} 最新{direction}参考价为 {price} {fiat}，'\
                        f'高于服务商参考均价 {average_price} {fiat}，请留意价格问题 {CONTACTS}'
                    else:
                        content = f'服务商 {name} {asset} 最新{direction}参考价为 {price} {fiat}，'\
                        f'低于服务商参考均价 {average_price} {fiat}，请留意价格问题 {CONTACTS}'
                    contents.append(content)
                    alert_cache.set(str(current_timestamp(to_int=True)))
    for content in contents:
        send_alert_notice(
            content=content,
            url=config["ADMIN_CONTACTS"]['fiat_notice'],
        )