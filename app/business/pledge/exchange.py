# -*- coding: utf-8 -*-
import json
from decimal import Decimal, ROUND_UP
from typing import Dict, List, Tuple, NamedTuple
from functools import cached_property

from flask import current_app
from app.models import db, Market
from app.models.pledge import (
    PledgeExchangeHistory,
    PledgePosition,
    PledgeExchangeOrderHistory,
    PledgeAssetTempOfflineUser,
)
from app.common import PrecisionEnum, OrderSideType, OrderOption, CeleryQueues
from app.business.order import Order
from app.business import (
    SiteSettings,
    ServerClient,
    lock_call,
    SYSTEM_ORDER_SOURCE,
    PriceManager,
)
from app.caches import MarketCache
from .helper import USDT_ASSET, PledgeValueHelper, get_loan_asset_info, get_cached_market_index_prices

from app.utils import now, quantize_amount, celery_task, amount_to_str, route_module_to_celery_queue
from app.utils.parser import JsonEncoder


route_module_to_celery_queue(__name__, CeleryQueues.PLEDGE)


class _ExchangeParam(NamedTuple):
    # 兑换挂单参数
    max_deviation: Decimal  # 最大价格偏差
    inc_deviation: Decimal  # 每次递增偏差
    inc_period: Decimal  # 递增时间间隔 秒


REPAY_EXCHANGE_PARAM = _ExchangeParam(
    max_deviation=Decimal("0.1"),
    inc_deviation=Decimal("0.002"),
    inc_period=Decimal("10"),
)
LIQ_EXCHANGE_PARAM = _ExchangeParam(
    max_deviation=Decimal("0.1"),
    inc_deviation=Decimal("0.005"),
    inc_period=Decimal("10"),
)


class ExchangeExecutor:

    MIN_EXCHANGE_USD = Decimal("0.01")  # 低于此市值可能不再兑换

    def __init__(self, exchange_id: int):
        self.exchange_id: int = exchange_id
        self.exchange_info: PledgeExchangeHistory = PledgeExchangeHistory.query.get(exchange_id)
        self.position: PledgePosition = PledgePosition.query.get(self.exchange_info.position_id)  # only for log
        #
        self.user_id = self.exchange_info.user_id
        self.account_id = get_loan_asset_info(self.position.loan_asset).account_id
        self.target_asset = self.position.loan_asset
        #
        self.client = ServerClient()
        self._market_info_dict = {}
        self.buy_price_deviation_ratio = Decimal()

    def get_market_info(self, market: str) -> dict:
        if market in self._market_info_dict:
            return self._market_info_dict[market]
        info = MarketCache(market).dict
        self._market_info_dict[market] = info
        return info

    @property
    def param(self) -> _ExchangeParam:
        if self.exchange_info.type == PledgeExchangeHistory.Type.REPAY:
            return REPAY_EXCHANGE_PARAM
        else:
            return LIQ_EXCHANGE_PARAM

    @cached_property
    def all_market_index_price_map(self) -> Dict[str, Decimal]:
        return get_cached_market_index_prices()

    def get_market_index_price(self, market: str) -> Decimal:
        return Decimal(self.all_market_index_price_map[market])

    def get_pledge_account_balances(self) -> Dict[str, Decimal]:
        """ 获取用户质押账户的余额 """
        balances = self.client.get_user_balances(self.user_id, account_id=self.account_id)
        result_balances = {
            asset: available
            for asset, balance_info in balances.items()
            if (available := (balance_info["available"])) > Decimal()
        }
        return result_balances

    def set_start_info(self):
        if not self.exchange_info.started_at:
            if not self.exchange_info.init_pledge_data:
                # 强平打断还币时，已经设置了
                asset_balance = self.get_pledge_account_balances()
                self.exchange_info.init_pledge_data = json.dumps(asset_balance, cls=JsonEncoder)
            self.exchange_info.started_at = now()
            db.session.commit()

    def stop_exchange(self):
        self.set_finish_info()
        self.notify_exchange_finished()

    def set_finish_info(self):
        if not self.exchange_info.finished_at:
            asset_balance = self.get_pledge_account_balances()
            self.exchange_info.finished_pledge_data = json.dumps(asset_balance, cls=JsonEncoder)
            self.exchange_info.finished_at = now()
            self.exchange_info.status = PledgeExchangeHistory.Status.FINISHED
            db.session.commit()

    def notify_exchange_finished(self):
        from app.business.pledge.liquidation import execute_position_liquidation_task
        from app.business.pledge.repay import execute_close_repay_task
        from app.business.pledge.repay import execute_partial_close_repay_task

        match self.exchange_info.type:
            case PledgeExchangeHistory.Type.REPAY:
                execute_close_repay_task.delay(self.position.id)
            case PledgeExchangeHistory.Type.LIQ:
                execute_position_liquidation_task.delay(self.position.id)
            case PledgeExchangeHistory.Type.PARTIAL_REPAY:
                execute_partial_close_repay_task.delay(self.position.id)
            case _:
                pass

    def execute(self):
        if self.exchange_info.status == PledgeExchangeHistory.Status.FINISHED:
            return
        if not (SiteSettings.spot_trading_enabled and SiteSettings.trading_enabled):
            return

        self.cancel_all_order()

        if offline_user:= PledgeAssetTempOfflineUser.get_by_user_id(self.user_id):
            if offline_user.type in (PledgeAssetTempOfflineUser.Type.ASSET_EXCHANGE, None):
                current_app.logger.warning(
                    f"pledge_exchange_executor position: {self.position.id} "
                    f"user_id: {self.user_id} pledge_asset_temp_offline_user"
                )
                return
        self.set_start_info()
        # 对于部分平仓的，根据exchange_detail中的数据进行兑换
        if self.exchange_info.type == PledgeExchangeHistory.Type.PARTIAL_REPAY:
            exchange_detail = json.loads(self.exchange_info.exchange_detail)
            asset_balance_dict = {k: Decimal(v) for k, v in exchange_detail["sell_amount_data"].items()}
            total_need_pay_amount = Decimal(exchange_detail["need_pay_amount"])
            _balance_map = self.get_pledge_account_balances()
            for k, v in asset_balance_dict.items():
                asset_balance_dict[k] = min(v, _balance_map.get(k, Decimal()))
            if USDT_ASSET not in asset_balance_dict and USDT_ASSET != self.target_asset:
                asset_balance_dict[USDT_ASSET] = _balance_map.get(USDT_ASSET, Decimal())
            asset_balance_dict = {k: v for k, v in asset_balance_dict.items() if v > Decimal()}

            target_asset_balance = _balance_map.pop(self.target_asset, Decimal())
        else:
            asset_balance_dict = self.get_pledge_account_balances()
            total_need_pay_amount = self.position.total_unflat_amount  # 借币币种的总待还

            target_asset_balance = asset_balance_dict.pop(self.target_asset, Decimal())
        diff_pay_amount = total_need_pay_amount - target_asset_balance  # 借币币种的差额数
        min_b = Decimal(10) ** -PrecisionEnum.COIN_PLACES
        if diff_pay_amount <= Decimal() or not any(v > min_b for v in asset_balance_dict.values()):
            # 足够还币了 or 无剩余的质押币种
            self.stop_exchange()
            return
        
        self.buy_price_deviation_ratio = self.calc_buy_price_deviation_ratio(diff_pay_amount, asset_balance_dict)
        order_params = self.build_order_params(asset_balance_dict, diff_pay_amount)
        if not order_params:
            # 有质押币的余额，但是没有挂单参数，说明是 无可用市场 或者 amount为0
            # 当剩余市值太小时不再兑换（amount为0时）
            asset_price_dict = PriceManager.assets_to_usd(list(asset_balance_dict))
            remain_usd = sum([asset_price_dict[a] * m for a, m in asset_balance_dict.items()])
            if remain_usd <= self.MIN_EXCHANGE_USD:
                self.stop_exchange()
                return
            else:
                current_app.logger.error(f"pledge_exchange_executor position: {self.position.id} order_params empty")

        for order_param in order_params:
            try:
                self.put_order(order_param)
            except Exception as e:
                current_app.logger.error(
                    f"pledge_exchange_executor position:{self.position.id} "
                    f"put_order failed. order_param: {order_param} error: {e!r}"
                )

    def build_order_params(self, asset_balance_dict: Dict[str, Decimal], diff_pay_amount: Decimal) -> List[Dict]:
        online_markets = MarketCache.list_online_markets()
        sorted_pledge_assets = PledgeValueHelper.sort_pledge_assets(asset_balance_dict)
        order_params = []
        for asset in sorted_pledge_assets:
            if (m0 := f"{asset}{USDT_ASSET}") in online_markets \
                    and f"{self.target_asset}{USDT_ASSET}" in online_markets:
                # ETHBTC这种市场不直接兑换，优先通过 ETHUSDT、BTCUSDT去兑换
                market = m0
                is_direct = False
            elif (m1 := f"{asset}{self.target_asset}") in online_markets:
                market = m1
                is_direct = True
            elif (m2 := f"{self.target_asset}{asset}") in online_markets:
                market = m2
                is_direct = True
            else:
                current_app.logger.error(
                    f"pledge_exchange_executor position: {self.position.id} "
                    f"asset: {asset} not found trade market"
                )
                continue

            market_info = self.get_market_info(market)
            if not market_info or market_info["status"] != Market.Status.ONLINE or market_info["trading_disabled"]:
                current_app.logger.error(
                    f"pledge_exchange_executor position: {self.position.id} "
                    f"asset: {asset} market: {market} status invalid"
                )
                continue

            asset_balance = asset_balance_dict[asset]
            if is_direct:
                # asset -> loan_asset
                side, price, amount, deviation = self.calc_order_param(
                    market, asset, asset_balance, self.target_asset, diff_pay_amount
                )
                delta_pay_amount = amount if side == OrderSideType.BUY else price * amount
                delta_pay_amount = quantize_amount(delta_pay_amount, PrecisionEnum.COIN_PLACES)  # 向下取整
            else:
                # asset -> usdt -> loan_asset 一定是先卖出 再买入
                # 先用需要买入loan_asset的数目，算出要支付的usdt数目
                m2 = f"{self.target_asset}{USDT_ASSET}"
                m2_price, _ = self.calc_price_and_deviation(m2, OrderSideType.BUY)
                m2_need_u_amount = m2_price * diff_pay_amount
                m2_need_u_amount = quantize_amount(m2_need_u_amount, PrecisionEnum.COIN_PLACES, ROUND_UP)
                # 再用需要得到的usdt数目，算出要卖出的质押币种asset的数目
                side, price, amount, deviation = self.calc_order_param(
                    market, asset, asset_balance, USDT_ASSET, m2_need_u_amount
                )
                assert side == OrderSideType.SELL
                # 质押币全卖了可能也不够，重新算下能够得到多少U，这些U能够得到买入多少借币币种
                delta_pay_amount = quantize_amount(price * amount / m2_price, PrecisionEnum.COIN_PLACES)  # 向下取整
            if amount > 0:
                order_params.append(
                    {
                        "market": market,
                        "side": side,
                        "price": price,
                        "amount": amount,
                        "deviation": deviation,
                    }
                )
                diff_pay_amount -= delta_pay_amount
            if diff_pay_amount <= 0:
                break
        return order_params

    def calc_buy_price_deviation_ratio(
        self,
        diff_pay_amount: Decimal,
        asset_balance_dict: Dict[str, Decimal],
    ) -> Decimal:
        """ 计算买单价格偏差的修正系数ratio """
        if self.target_asset == USDT_ASSET:
            return Decimal("1")
        if USDT_ASSET not in asset_balance_dict:
            return Decimal()

        assets = set(asset_balance_dict)
        assets.add(self.target_asset)
        asset_price_dict = PriceManager.assets_to_usd(assets)
        diff_pay_usd = diff_pay_amount * asset_price_dict[self.target_asset]
        can_buy_usd = asset_balance_dict[USDT_ASSET] * asset_price_dict[USDT_ASSET]
        can_sell_usd = sum(
            asset_price_dict[a] * amount for a, amount in asset_balance_dict.items() if a != USDT_ASSET
        )  # 可以用来挂卖单的质押币市值
        ratio_b = min(max(can_buy_usd / diff_pay_usd, Decimal()), Decimal("1"))
        ratio_b = quantize_amount(ratio_b, 2)
        if can_sell_usd + can_buy_usd > diff_pay_usd:
            # 未穿仓：当USDT越多，价格越接近正常偏差；当USDT越少，价格越平滑
            return ratio_b

        # 穿仓了：ratio_b应该接近1，剩余质押币应该很少
        ratio_s = min(max(can_sell_usd / diff_pay_usd, Decimal()), Decimal("1"))
        ratio_s = quantize_amount(ratio_s, 2)
        return max(ratio_b, (1 - ratio_s))

    def calc_sell_price_and_deviation(self, index_price: Decimal) -> Tuple[Decimal, Decimal]:
        """ 计算卖单价格 和 偏差 """
        # 卖出价格 = 最新价 * (1- 价格偏差) ，价格偏差=Min(时长 * 基本偏差，最大偏差)
        delta_ts = (now() - self.exchange_info.started_at).total_seconds()
        param = self.param
        # 第[0,10)秒，1个基本偏差；第[10,20)秒，2个基本偏差
        count = quantize_amount(Decimal(delta_ts) / param.inc_period, 0, ROUND_UP)
        deviation = count * param.inc_deviation
        deviation = quantize_amount(min(deviation, param.max_deviation), 8)
        return index_price * (1 - deviation), deviation

    def calc_buy_price_and_deviation(self, index_price: Decimal) -> Tuple[Decimal, Decimal]:
        """ 计算买单价格 和 偏差 """
        # 买入价格 = 最新价 * (1+价格偏差) ，价格偏差=Min(上次买入价格偏差 + 基本偏差 * ratio，最大偏差)
        last_buy_row: PledgeExchangeOrderHistory = PledgeExchangeOrderHistory.query.filter(
            PledgeExchangeOrderHistory.exchange_id == self.exchange_id,
            PledgeExchangeOrderHistory.side == OrderSideType.BUY.value,
        ).order_by(
            PledgeExchangeOrderHistory.id.desc(),
        ).with_entities(
            PledgeExchangeOrderHistory.price_deviation,
        ).first()
        index_price_deviation = last_buy_row.price_deviation if last_buy_row else Decimal()
        param = self.param
        deviation = index_price_deviation + param.inc_deviation * self.buy_price_deviation_ratio
        deviation = quantize_amount(min(deviation, param.max_deviation), 8)
        return index_price * (1 + deviation), deviation

    def calc_price_and_deviation(self, market: str, side: OrderSideType) -> Tuple[Decimal, Decimal]:
        index_price = self.get_market_index_price(market)
        if side == OrderSideType.BUY:
            price, v = self.calc_buy_price_and_deviation(index_price)
        else:
            price, v = self.calc_sell_price_and_deviation(index_price)
        price = quantize_amount(price, self.get_market_info(market)['quote_asset_precision'])
        return price, v

    def calc_order_param(
        self,
        market: str,
        source_asset: str,
        source_amount: Decimal,
        want_asset: str,
        want_amount: Decimal,
    ) -> Tuple[OrderSideType, Decimal, Decimal, Decimal]:
        market_info = self.get_market_info(market)
        if source_asset == market_info["quote_asset"] and want_asset == market_info["base_asset"]:
            side = OrderSideType.BUY
        elif source_asset == market_info["base_asset"] and want_asset == market_info["quote_asset"]:
            side = OrderSideType.SELL
        else:
            raise ValueError(f"error source:{source_asset} want:{want_asset} in market:{market}")

        price, deviation = self.calc_price_and_deviation(market, side)
        if side == OrderSideType.BUY:
            # usdt -> btc, 向下取整 不能超过余额
            can_buy_amount = quantize_amount(source_amount / price, PrecisionEnum.COIN_PLACES)
            amount = min(can_buy_amount, want_amount)
        else:
            # btc -> usdt，向上取整，尽量保证成交后得到的会更多
            precision = market_info["base_asset_precision"]
            need_sell_amount = quantize_amount(want_amount / price, precision, ROUND_UP)
            amount = min(source_amount, need_sell_amount)
        amount = quantize_amount(amount, PrecisionEnum.COIN_PLACES)
        return side, price, amount, deviation

    def put_order(self, param: dict):
        """ 不收手续费 """
        market = param["market"]
        market_info = self.get_market_info(market)
        side = param["side"].value
        amount = amount_to_str(param["amount"], market_info["base_asset_precision"])
        price = amount_to_str(param["price"], market_info["quote_asset_precision"])
        order_info = self.client.put_limit_order(
            user_id=self.user_id,
            account_id=self.account_id,
            market=market,
            side=side,
            amount=amount,
            price=price,
            taker_fee_rate="0",
            maker_fee_rate="0",
            source=Order.OrderSourceType.SYSTEM.value,
            fee_asset=None,
            fee_discount="0",
            option=OrderOption.HIDE | OrderOption.WITHOUT_ORDER_MIN_AMOUNT | OrderOption.SYSTEM,
        )
        order_his = PledgeExchangeOrderHistory(
            user_id=self.user_id,
            loan_asset=self.target_asset,
            exchange_id=self.exchange_id,
            market=market,
            order_id=order_info["id"],
            side=side,
            type=Order.NormalOrderType.LIMIT.value,
            price=price,
            amount=amount,
            price_deviation=param["deviation"],
        )
        db.session.add(order_his)
        db.session.commit()

    def cancel_all_order(self):
        """ 取消质押账户的全部订单 """
        # 目前server接口不支持 在某个账户下 取消全部市场的订单。先查出质押账户的全部订单，再按市场取消。
        all_orders = []
        page = 1
        while True:
            page_orders = self.client.user_pending_orders(
                self.user_id, account_id=self.account_id, limit=100, page=page
            )
            all_orders.extend(page_orders)
            page += 1
            if not page_orders.has_next:
                break
        markets = {o["market"] for o in all_orders}
        for m in markets:
            self.client.cancel_user_all_order(
                user_id=self.user_id,
                account_id=self.account_id,
                market=m,
                source_option=SYSTEM_ORDER_SOURCE,
            )


@celery_task
@lock_call(with_args=True)
def execute_position_exchange_task(position_id: int):
    """ 执行仓位的兑换 """
    exchange_his_rows: List[PledgeExchangeHistory] = PledgeExchangeHistory.query.filter(
        PledgeExchangeHistory.position_id == position_id,
        PledgeExchangeHistory.status == PledgeExchangeHistory.Status.RUNNING,
    ).order_by(PledgeExchangeHistory.id.asc()).all()
    his_num = len(exchange_his_rows)
    if not his_num:
        return

    his = None
    if his_num == 1:
        his = exchange_his_rows[0]
    elif his_num == 2:
        # 多条兑换信息时，则优先用强平的
        exchange_type_row_map = {i.type: i for i in exchange_his_rows}
        his = exchange_type_row_map.get(PledgeExchangeHistory.Type.LIQ, None)
    if not his:
        raise ValueError(f"execute_position_exchange_task pos:{position_id} exchange_his error {his_num} rows")

    executor = ExchangeExecutor(his.id)
    executor.execute()
