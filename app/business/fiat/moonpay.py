#!/usr/bin/env python3

import uuid
from base64 import b64encode
from decimal import Decimal
from hashlib import sha256
from hmac import HMAC
from typing import Any, Dict, Optional
from urllib.parse import quote as url_quote

from ...caches.fiat import FiatPriceCache
from ...config import config
from ...models import User, FiatOrder
from ...utils import (AmountType, RESTClient, amount_to_str, quantize_amount,
                      url_join, g_map)
from ...utils import AWSBucketPublic
from .base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType


class MoonPayClient(BasePartnerClient):

    name = 'MoonPay'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_moonpay.png')
    buy_assets = ['BTC', 'BCH', 'ETH', 'LTC', 'USDT', 'USDC', 'TRX']
    buy_fiats = [
        'USD',
        'BGN', 'BRL', 'CAD', 'CHF', 'COP', 'CZK', 'DKK', 'GBP',
        'IDR', 'ILS', 'JOD', 'KES', 'KWD', 'LKR',
        'MXN', 'NOK', 'NZD', 'OMR', 'PEN', 'PLN', 'SEK',
        'THB', 'VND', 'ZAR', 'TRY', 'AUD', 'HKD',
        'RON', 'EGP', 'TWD', 'NGN'
    ]
    sell_fiats = ['USD', 'GBP']
    sell_assets = ['BTC', 'ETH', 'USDT', 'USDC']
    support_types = [SupportType.BUY, SupportType.SELL]
    buy_payment_methods = [PaymentMethod.APPLE_PAY, PaymentMethod.GOOGLE_PAY, PaymentMethod.PAYPAL, PaymentMethod.VISA]
    sell_payment_methods = [PaymentMethod.PAYPAL, PaymentMethod.SEPA, ]
    buy_quote_methods = [QuoteMethod.BY_FIAT]
    sell_quote_methods = [QuoteMethod.BY_ASSET]
    buy_fee_rate = Decimal('0.045')
    sell_fee_rate = Decimal('0.01')
    min_fee = Decimal('3.99')

    buy_order_limit_min = Decimal(50)
    buy_order_limit_max = Decimal(10000)
    sell_order_limit_max = Decimal(10000)
    sell_order_limit_min = Decimal(100)
    daily_limit = Decimal(10000)
    monthly_limit = Decimal(50000)
    help_url = 'https://support.moonpay.io/hc/en-gb/requests/new'

    network_map = {
        'USDT': 'TRC20',
        'USDC': 'ERC20',
    }

    _blockchain_map = {
        ('USDT', 'TRC20'): 'usdt_trx',
        ('USDT', 'ERC20'): 'usdt',
        ('USDC', 'ERC20'): 'usdc',
    }

    def __init__(self):
        self._conf = config['MOONPAY_CONFIG']
        self._client = RESTClient(self._conf['url'])
        secret = self._conf['secret']
        self._server_client = RESTClient(self._conf['url'], headers={'Authorization': f'Api-Key {secret}'})

    def _sign_url(self, url: str):
        ix = url.index('?')
        query = url[ix:]
        sig = HMAC(self._conf['secret'].encode(), query.encode(), sha256).digest()
        return b64encode(sig)

    def get_prices(self):
        def _get_prices(self_assets, support_type):
            assets = self_assets[:]
            api_data = {}
            support_types = [support_type] * len(assets)
            prices = g_map(self._get_price, assets, support_types, ordered=True, fail_safe=Decimal(), size=5)
            for asset, map_price in zip(assets, prices):
                if not map_price:
                    continue
                for fiat, price in map_price.items():
                    api_data[f"{support_type.value}-{asset}-{fiat}"] = amount_to_str(price, 2)
            return api_data

        buy_prices = _get_prices(self.buy_assets, SupportType.BUY)
        sell_prices = _get_prices(self.sell_assets, SupportType.SELL)
        return {**buy_prices, **sell_prices}

    def _get_price(self, asset: str, support_type) -> Dict[str, str]:
        if support_type is SupportType.BUY:
            ask_or_bid = 'ask_price'
            fiats = self.buy_fiats
        else:
            ask_or_bid = 'bid_price'
            fiats = self.sell_fiats
        r = self._client.get(f'/v3/currencies/{asset.lower()}/{ask_or_bid}', apiKey=self._conf['apikey'])
        return {k: v for k, v in r.items() if k in fiats}

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        if support_type is SupportType.BUY:
            return quantize_amount(cache.get_price(asset, fiat) * (1 + self.buy_fee_rate), self._price_precision(asset))
        return quantize_amount(
            cache.get_price(asset, fiat, support_type) * (1 - self.sell_fee_rate),
            self._price_precision(asset)
        )

    def fiat_to_asset_amount(self, fiat: str, asset: str,
                             fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self,
              user: User,
              from_: str,
              to: str,
              amount: AmountType,
              support_type=SupportType.BUY) -> Quote:
        if support_type is SupportType.BUY:
            return self.post_buy_quote(from_, to, amount, support_type)
        return self.post_sell_quote(from_, to, amount, support_type)

    def post_buy_quote(self, from_, to, amount, support_type):
        if from_ in self.buy_assets and to in self.buy_fiats:
            raise NotImplementedError(f'quote by {from_}-{to} is not supported')
        elif from_ in self.buy_fiats and to in self.buy_assets:
            asset = to
            fiat = from_
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        chain = self.get_asset_chain(asset)
        asset_to_quote = self._blockchain_map.get((asset, chain)) or asset.lower()
        r = self._client.get(f'/v3/currencies/{asset_to_quote}/buy_quote', **{
            'apiKey': self._conf['apikey'],
            'baseCurrencyCode': fiat.lower(),
            'baseCurrencyAmount': amount_to_str(amount),
            'areFeesIncluded': 'true',
        })
        asset_amount = Decimal(str(r['quoteCurrencyAmount']))
        fiat_amount = Decimal(str(r['totalAmount']))
        real_fiat_amount = fiat_amount
        price = Decimal()
        if asset_amount > 0:
            if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
                real_fiat_amount -= (self.min_fee - fee)
                if real_fiat_amount < 0:
                    real_fiat_amount = 0
            price = quantize_amount(real_fiat_amount / asset_amount, self._price_precision(asset))

        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=fiat_amount,
            price=price,
            support_type=support_type
        )

    def post_sell_quote(self, from_, to, amount, support_type):
        if from_ in self.sell_fiats and to in self.sell_assets:
            raise NotImplementedError(f'quote by {from_}-{to} is not supported')
        elif from_ in self.sell_assets and to in self.sell_fiats:
            asset = from_
            fiat = to
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        chain = self.get_asset_chain(asset)
        asset_to_quote = self._blockchain_map.get((asset, chain)) or asset.lower()
        r = self._client.get(f'/v3/currencies/{asset_to_quote}/sell_quote', **{
            'apiKey': self._conf['apikey'],
            'quoteCurrencyCode': fiat.lower(),
            'baseCurrencyAmount': amount_to_str(amount),
            'lockAmount': True,
        })
        asset_amount = Decimal(str(r['baseCurrencyAmount']))
        fiat_amount = Decimal(str(r['quoteCurrencyAmount']))
        real_fiat_amount = fiat_amount
        price = Decimal()
        if asset_amount > 0:
            price = quantize_amount(real_fiat_amount / asset_amount, self._price_precision(asset))

        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(fiat_amount, 2),
            price=price,
            support_type=support_type
        )

    def place_order(self,
                    user: User,
                    quote: Quote,
                    address: str) -> Order:
        support_type = SupportType(quote.support_type)
        data = {
            'apiKey': self._conf['apikey'],
            'externalTransactionId': str(uuid.uuid4())
        }
        chain = self.get_asset_chain(quote.asset)
        asset_to_order = self._blockchain_map.get((quote.asset, chain)) or quote.asset
        if support_type is SupportType.BUY:
            data.update({
                'currencyCode': asset_to_order,
                'baseCurrencyCode': quote.fiat,
                'baseCurrencyAmount': quote.fiat_amount,
                'walletAddress': address,
            })
            order_url = self._conf['payment_url']['buy']
        else:
            data.update({
                'baseCurrencyCode': asset_to_order,
                'baseCurrencyAmount': quote.asset_amount,
                'quoteCurrencyCode': quote.fiat,
                'refundWalletAddress': address,
            })
            order_url = self._conf['payment_url']['sell']

        if user.email:
            data['email'] = user.email

        payment_url = url_join(order_url, '', **data)
        sig = self._sign_url(payment_url)
        payment_url += f'&signature={url_quote(sig)}'
        return Order(
            id=data['externalTransactionId'],
            payment_url=payment_url,
            extra={}
        )

    def get_order(self, order_id: str, order_type: FiatOrder.OrderType) -> Optional[Dict[str, Any]]:
        if order_type is FiatOrder.OrderType.BUY:
            r = self._server_client.get('/v1/transactions', externalTransactionId=order_id)
        else:
            r = self._server_client.get('/v3/sell_transactions', externalTransactionId=order_id)
        return r[0] if r else None

    def get_supported_fiats(self):
        currencies = self._server_client.get('/v3/currencies')
        return [currency['code'].upper() for currency in currencies if currency['type'] == 'fiat']

    def get_supported_assets(self):
        currencies = self._server_client.get('/v3/currencies')
        return [currency['code'].upper() for currency in currencies if currency['type'] == 'crypto']
