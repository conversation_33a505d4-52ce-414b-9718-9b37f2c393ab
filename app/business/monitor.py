# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timedelta, datetime
import time
from decimal import Decimal
from enum import Enum
from typing import NamedTuple, Dict, Optional, Tuple
from dateutil import tz
from sqlalchemy import func
from flask import current_app

from app import config
from app.business.site import BusinessSettings
from app.business.alert import send_alert_notice
from app.business.fiat import get_fiat_partners
from app.business.risk_control import add_risk_user
from app.business.risk_control.base import RiskControlGroupConfig
from app.caches import MarketCache
from app.caches.spot import SpotBigBookingCache
from app.caches.risk_control import RiskControlDeteriorateCache
from app.common import OrderSideType, CeleryQueues, MarketStatusType
from app.models import Deposit, User, SubAccount, FiatOrder, RiskUser, SpotAssetTradeSummary, BusinessSystemUserRecord
from app.models.spot import SystemAssetLiability
from app.models.exchange import AssetExchangeOrder
from app.utils import now, current_timestamp, amount_to_str, batch_iter, today, celery_task
from app.business import ServerClient, PriceManager, cached, lock_call
from app.business.amm import LiquidityService
from app.utils.date_ import yesterday
from app.exceptions import ServiceUnavailable
from app.assets.asset import get_asset_config


class DepositAmountMonitor(object):
    """ 充值数目监控 """

    asset: str = None

    class MonitorType(Enum):
        SINGLE = "single"  # 单条
        TIME_RANGE = "time_range"  # 时间段

    # query method start
    @classmethod
    def query_single_deposit_amount_gte_value(cls, min_amount, start_time):
        """ 单笔充值大于等于value """
        records = Deposit.query.filter(
            Deposit.asset == cls.asset,
            Deposit.status == Deposit.Status.FINISHED,
            Deposit.amount >= min_amount,
            Deposit.created_at >= start_time,
        ).all()
        return records

    @classmethod
    def query_deposit_time_range_total_amount_gte_value(
        cls, min_amount, start_time, end_time
    ):
        """ 某个时间段内的所有充值总额大于等于value """
        records = (
            Deposit.query.with_entities(
                Deposit.user_id,
                func.sum(Deposit.amount).label("total_amount"),
                func.max(Deposit.id).label("max_id"),
            )
            .filter(
                Deposit.asset == cls.asset,
                Deposit.status == Deposit.Status.FINISHED,
                Deposit.created_at >= start_time,
                Deposit.created_at <= end_time,
            )
            .group_by(Deposit.user_id)
            .having(func.sum(Deposit.amount) >= min_amount)
            .all()
        )
        return records

    @classmethod
    def get_user_email(cls, user_ids):
        users = User.query.filter(User.id.in_(user_ids)).with_entities(
            User.id, User.email
        )
        return {u.id: u.email for u in users}

    @classmethod
    def format_number(cls, num: Decimal) -> str:
        # 1000000.00 --> 1,000,000
        new_num = (
            num.to_integral() if num == num.to_integral() else num.normalize()
        )
        return "{:,}".format(new_num)


DepositAmountMonitorRule = NamedTuple(
    "DepositAmountMonitorRule",
    [
        ("rule_id", int),  # identity
        ("type", DepositAmountMonitor.MonitorType),
        ("timedelta_seconds", int),
        ("notice_amount", Decimal),
        ("notice_delta_seconds", int),
    ],
)


class CetDepositAmountMonitor(DepositAmountMonitor):
    """ CET告警 大额CET充值监控 """
    asset = "CET"

    @classmethod
    def monitor_single(cls, min_amount: Decimal, start_time: datetime) -> Dict:
        now_ = now()
        now_utc8 = now_.astimezone(tz.gettz("UTC+8"))
        now_utc8_str = now_utc8.strftime("%Y-%m-%d %H:%M:%S")
        records = cls.query_single_deposit_amount_gte_value(
            min_amount, start_time
        )
        user_email_map = cls.get_user_email({i.user_id for i in records})

        msg_template = "\n".join(
            [
                "{now_utc8_str}（UTC+8）",
                "账号：用户ID {user_id}  邮箱 {email}",
                "触发了：单笔充值达{min_amount_str} CET",
                "用户对应数据：{amount_str} CET",
            ]
        )
        notice_msg_map = {}
        for r in records:
            user_id = r.user_id
            email = user_email_map.get(user_id)
            if email and email in BusinessSettings.site_monitor_internal_whitelist_emails:
                continue
            template_data = {
                "now_utc8_str": now_utc8_str,
                "user_id": user_id,
                "email": email,
                "amount_str": cls.format_number(r.amount),
                "min_amount_str": cls.format_number(min_amount),
            }
            notice_msg_map[r.id] = msg_template.format(**template_data)
        return notice_msg_map

    @classmethod
    def monitor_time_range(
        cls, min_amount: Decimal, start_time: datetime, end_time: datetime
    ) -> Dict:
        now_ = now()
        now_utc8 = now_.astimezone(tz.gettz("UTC+8"))
        now_utc8_str = now_utc8.strftime("%Y-%m-%d %H:%M:%S")
        hour_delta = int((end_time - start_time).total_seconds() // 3600)

        records = cls.query_deposit_time_range_total_amount_gte_value(
            min_amount, start_time, end_time
        )
        user_email_map = cls.get_user_email({i.user_id for i in records})

        msg_template = "\n".join(
            [
                "{now_utc8_str}（UTC+8）",
                "账号：用户ID {user_id}  邮箱 {email}",
                "触发了：{hour_delta}小时内充值达{min_amount_str} CET",
                "用户对应数据：{amount_str} CET",
            ]
        )
        notice_msg_map = {}
        for r in records:
            user_id = r.user_id
            email = user_email_map.get(user_id)
            if email and email in BusinessSettings.site_monitor_internal_whitelist_emails:
                continue
            template_data = {
                "now_utc8_str": now_utc8_str,
                "user_id": user_id,
                "hour_delta": hour_delta,
                "email": email,
                "amount_str": cls.format_number(r.total_amount),
                "min_amount_str": cls.format_number(min_amount),
            }
            # 带上max_id，每次充值都通知
            key_ = "{}-{}".format(user_id, r.max_id)
            notice_msg_map[key_] = msg_template.format(**template_data)
        return notice_msg_map

    @classmethod
    def run(cls, rule: DepositAmountMonitorRule) -> Dict:
        now_ = now()
        start_time = now_ - timedelta(seconds=rule.timedelta_seconds)
        min_amount = rule.notice_amount

        if rule.type == DepositAmountMonitor.MonitorType.SINGLE:
            return cls.monitor_single(min_amount, start_time)
        elif rule.type == DepositAmountMonitor.MonitorType.TIME_RANGE:
            return cls.monitor_time_range(min_amount, start_time, now_)


class AmmMonitor:
    # 资金量所允许的最大基准价差
    PRICE_DIFFS = (
        (5000, Decimal('0.02')),
        (10000, Decimal('0.01')),
        (20000, Decimal('0.007')),
        (40000, Decimal('0.005')),
        (70000, Decimal('0.003')),
        (100000, Decimal('0.002'))
    )
    MIN_DIFF = Decimal('0.001')
    # 价差系数
    N_DIFF = 3

    def __init__(self, market: str):
        self.srv = LiquidityService(market)

    def monitor_orders(self) -> Optional[str]:
        if self.srv.pool.liquidity == 0:
            return None
        market_name = self.srv.market['name']
        market_status = ServerClient().get_market_maintain_status(market_name)['status']
        if market_status in (
            MarketStatusType.MARKET_STATUS_STOP,
            MarketStatusType.MARKET_STATUS_PROTECTION,
        ):
            # 市场处于停服状态以及保护期不监控
            return None
        for _ in range(2):
            r = ServerClient().user_pending_orders(self.srv.pool.system_user_id, market_name, page=1, limit=1)
            if len(r) != 0:
                return None
            time.sleep(3)
        # 如果是资金不足以挂单，则不告警
        liq_usd = self.srv.asset_amounts_to_usd(*self.srv.get_assets_amount())
        if liq_usd < self.srv.MIN_LIQUIDITY_USD * 2:
            return None
        msg = f"amm market {market_name} has no orders"
        current_app.logger.warning(msg)
        return msg

    def monitor_price(self) -> Optional[str]:
        """检查资金池价格与最新成交价的价差，过大则重新铺单"""
        market_name = self.srv.market['name']
        base, quote = self.srv.get_assets_amount()
        if base == 0 or quote == 0:
            return None
        usd = self.srv.asset_amounts_to_usd(base, quote)
        delta = self.MIN_DIFF
        for v, d in self.PRICE_DIFFS:
            if usd <= v:
                delta = d
                break
        last = ServerClient().market_last(market_name)
        diff = abs((quote / base - last) / last)
        if diff > delta * self.N_DIFF:
            current_app.logger.info('amm market %s price diff is too large, replace orders.', market_name)
            LiquidityService.replace_orders(market_name)
        # no alert currently
        return None


class AssetLiabilityMonitor:

    def __init__(self, asset: str):
        self.asset = asset

    @classmethod
    def _monitor_all_three_positive(cls, threshold: Decimal) -> bool:
        """判断平台权益是否连续三次为正(满足阈值条件)"""
        asset = 'ALL'
        rows = SystemAssetLiability.query.filter(
            SystemAssetLiability.asset == asset
        ).order_by(SystemAssetLiability.id.desc()).limit(3).all()
        if len(rows) < 3:
            return False

        for row in rows:
            if row.sys_income < 0 or abs(row.sys_income) < threshold:
                return False
        return True

    @classmethod
    def monitor_all(cls) -> Tuple[bool, bool, str, Decimal]:
        """
        判断全站总平台权益市值是否大于设定的阈值, 返回值为是否触发预警、预警内容、平台权益市值
        返回值: 是否触发、是否恢复、告警内容、平台权益
        """
        reminds = config['ADMIN_CONTACTS']['slack_at'].get('asset_liability_all_notices', [])
        asset_liability_conf = RiskControlGroupConfig().asset_liability_not_equal
        threshold = asset_liability_conf['asset_liability_not_equal_threshold']
        recover_at = asset_liability_conf['asset_liability_risk_recover_at']
        deteriorate_rate = asset_liability_conf['deteriorate_rate']
        asset = 'ALL'
        last = SystemAssetLiability.query.filter(
            SystemAssetLiability.asset == asset
        ).order_by(SystemAssetLiability.id.desc()).first()
        if not last:
            return False, True, '', Decimal()

        sys_income = last.sys_income
        risk_cache = RiskControlDeteriorateCache(RiskControlDeteriorateCache.BusinessEnum.ASSET_LIABILITY)
        is_exceed_threshold = abs(sys_income) >= threshold
        is_three_positive = cls._monitor_all_three_positive(threshold)
        is_deteriorate = risk_cache.is_deteriorate(
            asset,
            sys_income,
            threshold * deteriorate_rate / Decimal(100),
            not is_exceed_threshold or (sys_income > 0 and not is_three_positive),
            recover_at,
        )
        if not is_deteriorate:
            return False, not is_exceed_threshold, '', Decimal()

        content = '\n'.join([
            "【资产负债不平】",
            "全站维度资产负债不平",
            f"权益市值：{amount_to_str(sys_income)} USD（阈值为{amount_to_str(threshold)} USD）",
        ])
        result = False
        if sys_income < 0 or is_three_positive:
            content += "\n已限制全站提现，请及时处理"
            content = cls.add_remind_content(content, reminds)
            result = True
        return result, not is_exceed_threshold, content, last.sys_income

    @classmethod
    def _monitor_coin_judge(
            cls,
            row: SystemAssetLiability,
            coin_asset_liability_rate: Decimal,
            coin_asset_threshold: Decimal,
            price: Decimal,
    ):
        abs_sys_income = abs(row.sys_income)
        income_usd = abs_sys_income * price
        asset_liability_threshold = row.sys_debt * coin_asset_liability_rate / Decimal(100)
        return abs_sys_income >= asset_liability_threshold and income_usd >= coin_asset_threshold

    def _monitor_coin_three_positive(
            self,
            coin_asset_liability_rate: Decimal,
            coin_asset_threshold: Decimal,
            price: Decimal,
    ) -> bool:
        """判断平台权益是否连续三次为正(满足阈值条件)"""
        rows = SystemAssetLiability.query.filter(
            SystemAssetLiability.asset == self.asset
        ).order_by(SystemAssetLiability.id.desc()).limit(3).all()
        if len(rows) < 3:
            return False

        for row in rows:
            if row.sys_income < 0 or not self._monitor_coin_judge(
                    row, coin_asset_liability_rate, coin_asset_threshold, price
            ):
                return False
        return True

    def monitor_coin(self, is_check: bool) -> Tuple[bool, bool, str, Decimal, Decimal, Decimal]:
        """
        币种维度资产负债监控，需同时满足平台权益绝对值 ≥ 平台负债 * x% & 平台权益绝对值市值 ≥ y USD
        平台权益为正时仅告警，平台权益为负或连续三次为正时告警+限制
        is_check为True时, 不进行恶化程度检查, False时才进行恶化程度检查
        返回值: 是否触发、是否恢复、告警内容、平台权益、平台权益(USD)、阈值
        """
        reminds = config['ADMIN_CONTACTS']['slack_at'].get('asset_liability_asset_notices', [])
        coin_asset_liability_conf = RiskControlGroupConfig().coin_asset_liability_not_equal
        coin_asset_liability_rate = coin_asset_liability_conf['coin_asset_liability_rate']  # x%
        coin_asset_threshold = coin_asset_liability_conf['coin_asset_threshold']  # y USD
        coin_deteriorate_rate = coin_asset_liability_conf['coin_deteriorate_rate']
        asset_config = get_asset_config(self.asset)
        recover_at = asset_config.asset_liability_monitor_recover_at

        last = SystemAssetLiability.query.filter(
            SystemAssetLiability.asset == self.asset
        ).order_by(SystemAssetLiability.id.desc()).first()
        if not last:
            return False, True, '', Decimal(), Decimal(), Decimal()

        sys_income = last.sys_income
        price = PriceManager.asset_to_usd(self.asset)
        income_usd = sys_income * price
        asset_liability_threshold = abs(last.sys_debt) * coin_asset_liability_rate / Decimal(100)

        risk_cache = RiskControlDeteriorateCache(RiskControlDeteriorateCache.BusinessEnum.ASSET_LIABILITY)
        is_exceed_threshold = self._monitor_coin_judge(last, coin_asset_liability_rate, coin_asset_threshold, price)
        is_three_positive = self._monitor_coin_three_positive(coin_asset_liability_rate, coin_asset_threshold, price)
        is_normal = not is_exceed_threshold or (sys_income > 0 and not is_three_positive)
        is_deteriorate = not is_normal
        if not is_check:
            is_deteriorate = risk_cache.is_deteriorate(
                self.asset,
                sys_income,
                asset_liability_threshold * coin_deteriorate_rate / Decimal(100),
                is_normal,
                recover_at,
            )
        elif is_normal:
            risk_cache.reset(self.asset)
        if not is_deteriorate:
            return False, not is_exceed_threshold, '', sys_income, income_usd, asset_liability_threshold

        income_str = (f"平台权益：{amount_to_str(sys_income)} {self.asset}"
                      f"（阈值为平台负债*{coin_asset_liability_rate}%={asset_liability_threshold}）")
        content = '\n'.join([
            f"币种：{self.asset}",
            income_str,
            f"权益市值：{amount_to_str(income_usd, 2)} USD（阈值为{amount_to_str(coin_asset_threshold)} USD）",
        ])

        result = False
        if sys_income < 0 or is_three_positive:
            content += "\n已限制该币种提现，请及时处理"
            content = self.add_remind_content(content, reminds)
            result = True
        return result, not is_exceed_threshold, content, sys_income, income_usd, asset_liability_threshold

    @classmethod
    def add_remind_content(cls, content, reminds: list[str] = None):
        if reminds is None:
            reminds = []
        remind_str = '\n'
        for developer in reminds:
            remind_str += f'<@{developer}> '
        content += remind_str
        return content


class ServerHealthMonitor:

    class Health(Enum):
        OK = "ok"
        UNAVAILABLE = "服务不可用"
        NO_DEALS = "没有撮合成交"

    def __init__(self, server_client) -> None:
        self.sever_client = server_client

    def healthz(self) -> Health:
        now = current_timestamp(to_int=True)
        now -= now % 60
        # noinspection PyBroadException
        try:
            r = self.sever_client.query_deal_summary(now - 60, now)
        except (ServerClient.BadResponse, ServiceUnavailable):
            return self.Health.UNAVAILABLE
        if r['deal_count'] == 0:
            try:
                in_protect = self.sever_client.get_protect_status()['status']
            except (ServerClient.BadResponse, ServiceUnavailable):
                in_protect = False
            if not in_protect:
                return self.Health.NO_DEALS
        return self.Health.OK


class BookOrderFetcher:

    def __init__(self, markets):
        self.markets = markets
        self.c = ServerClient()

    def fetch_side_orders(self, side: OrderSideType):
        orders = []
        for market in self.markets:
            order_data = self.fetch_market_orders(market, side)
            orders.extend(order_data)
        return orders

    def fetch_market_orders(self, market, side):
        order_data = []
        has_next_page = True
        page = 1
        limit = 100
        while has_next_page:
            order_books = self.c.market_book_orders(
                market=market,
                side=side,
                page=page,
                limit=limit
            )
            for order_book in order_books:
                order_data.append(dict(
                    user_id=order_book['user'],
                    market=order_book['market'],
                    amount=order_book['amount'],
                    price=order_book['price'],
                ))
            has_next_page = order_books.has_next
            page += 1
        return order_data

    def fetch_market_last(self):
        market_last = {}
        for market in self.markets:
            price = self.c.market_last(market)
            market_last[market] = price
        return market_last


class _AlertHelper:

    @classmethod
    def get_sub_to_main_user(cls):
        rows = SubAccount.query.with_entities(SubAccount.user_id, SubAccount.main_user_id).all()
        return dict(rows)

    @classmethod
    def get_user_email_map(cls, user_ids) -> Dict:
        user_email_map = {}
        for chunk_user_ids in batch_iter(user_ids, 1000):
            chunk_users = (
                User.query.filter(User.id.in_(chunk_user_ids))
                .with_entities(
                    User.id,
                    User.email,
                )
                .all()
            )
            user_email_map.update({u.id: u.email for u in chunk_users})
        return user_email_map

    @classmethod
    def build_alert_text(cls, user_id: int, email: str, title, content):
        ct = now()
        dt = ct.astimezone(tz.gettz('UTC+8'))
        return f"""
    {dt.strftime('%Y-%m-%d %H:%M:%S')} (UTC+8)
    用户ID：{user_id}
    邮箱：{email}
    触发了： {title}
    用户对应的数据:
    {content}
        """


def get_main_user_order_data(markets: list, price_ratio_map: dict, side):
    sub_to_main = _AlertHelper.get_sub_to_main_user()
    fetcher = BookOrderFetcher(markets)
    sell_orders = fetcher.fetch_side_orders(side)
    price_range = get_market_price_range(markets, price_ratio_map)
    to_main_user_data = defaultdict(list)
    for sell_order in sell_orders:
        price = Decimal(sell_order['price'])
        price_down, price_up = price_range[sell_order['market']]
        if price_down <= price <= price_up:
            user_id = sell_order['user_id']
            user_id = sub_to_main.get(user_id) or user_id
            to_main_user_data[user_id].append(sell_order)
    return to_main_user_data


def get_market_price_range(markets, price_ratio_map):
    price_range = {}
    market_last = BookOrderFetcher(markets).fetch_market_last()
    for market, price in market_last.items():
        price_delta = price * price_ratio_map[market]
        price_down = price - price_delta
        price_up = price + price_delta
        price_range[market] = (price_down, price_up)
    return price_range


class CetAlertMonitor:
    """ CET告警 大额CET挂单监控 """
    threshold = Decimal('1000000')
    markets = ('CETUSDT', 'CETUSDC', 'CETBTC', 'CETBCH',)
    timeout = 60 * 60 * 24
    msg_id_sf = 'cet_alert_monitor:sell:{}'
    price_ratio = {market: Decimal('0.2') for market in markets}  # 抓取的价差比率 (-20%, +20%)

    @classmethod
    def send_sell_order_alert(cls):
        url = config["ADMIN_CONTACTS"]['cet_notice']
        if not url:
            return
        ignore_emails = BusinessSettings.site_monitor_internal_whitelist_emails
        monitored_data = cls.get_sell_monitored_data()
        user_emails = _AlertHelper.get_user_email_map(list(monitored_data.keys()))
        title = f'当前普通委托卖出挂单达{cls.threshold / 10000}万CET'
        for user_id, data in monitored_data.items():
            email = user_emails.get(user_id) or '-'
            if email in ignore_emails:
                continue
            content = f''
            for market, amount in data.items():
                content += f'{market}挂单：{amount_to_str(amount, 2)} CET\n'

            alert_text = _AlertHelper.build_alert_text(user_id, email, title, content)
            send_alert_notice(
                content=alert_text,
                url=url,
                expired_seconds=cls.timeout,
                msg_id=cls.msg_id_sf.format(user_id),
            )

    @classmethod
    def get_sell_monitored_data(cls):
        main_user_data = get_main_user_order_data(cls.get_markets(), cls.price_ratio, OrderSideType.SELL)
        monitored_data = {}
        for main_user_id, orders in main_user_data.items():
            amount = sum([Decimal(order['amount']) for order in orders])
            if amount >= cls.threshold:
                group_by_market = defaultdict(list)
                for order in orders:
                    group_by_market[order['market']].append(order)
                _ret = {}
                for market, group_orders in group_by_market.items():
                    _ret[market] = sum([Decimal(order['amount']) for order in group_orders])

                monitored_data[main_user_id] = _ret
        
        exchange_data = cls.get_exchange_sell_monitored_data()
        for main_user_id, orders in exchange_data.items():
            amount = sum([Decimal(order['source_amount']) for order in orders])
            if amount >= cls.threshold:
                group_by_market = defaultdict(list)
                for order in orders:
                    group_by_market[f'{order["source_asset"]}兑{order["target_asset"]}'].append(order)
                _ret = {}
                for market, group_orders in group_by_market.items():
                    _ret[market] = sum([Decimal(order['source_amount']) for order in group_orders])
                if main_user_id in monitored_data:
                    monitored_data[main_user_id].update(_ret)
                else:
                    monitored_data[main_user_id] = _ret
        return monitored_data

    @classmethod
    def get_exchange_sell_monitored_data(cls):
        user_result = defaultdict(list)

        orders = AssetExchangeOrder.query.filter(
            AssetExchangeOrder.status == AssetExchangeOrder.Status.EXCHANGING,
            AssetExchangeOrder.source_asset == "CET",
        ).with_entities(
            AssetExchangeOrder.user_id,
            AssetExchangeOrder.source_asset,
            AssetExchangeOrder.source_asset_amount,
            AssetExchangeOrder.target_asset,
        ).all()
        for order in orders:
            user_id = order.user_id
            user_result[user_id].append({
                "source_asset": order.source_asset,
                "source_amount": order.source_asset_amount,
                "target_asset": order.target_asset,
            })
        return user_result

    @classmethod
    def get_markets(cls):
        online_markets = MarketCache.list_online_markets()
        return tuple([market for market in cls.markets if market in online_markets])


class SpotBigBookingOrderMonitor:
    # 币币交易大额挂掉监控
    timeout = 60 * 60 * 24
    msg_id_sf = 'spot_booking_monitor:{}:{}:{}'
    side_map = {OrderSideType.BUY: "买", OrderSideType.SELL: "卖"}

    @classmethod
    def get_config_value(cls, amount, config_list) -> Decimal:
        for _amount, value in config_list:
            if Decimal(amount) > Decimal(_amount):
                return Decimal(value)
        return Decimal(config_list[-1][1])

    @classmethod
    @cached(1800)
    def get_ignore_user_set(cls):
        model = User
        rows = model.query.filter(
            User.user_type.in_([
                model.UserType.INTERNAL_MAKER,
                model.UserType.EXTERNAL_MAKER,
                model.UserType.EXTERNAL_SPOT_MAKER,
                model.UserType.EXTERNAL_CONTRACT_MAKER,
            ])
        ).with_entities(
            model.id,
        )
        # 特殊配置-业务账号
        system_model = BusinessSystemUserRecord
        system_rows = system_model.query.filter(
            system_model.status == system_model.Status.VALID
        ).with_entities(system_model.user_id)
        user_id_set = {i.id for i in rows} | {i.user_id for i in system_rows}
        user_id_set.add(config['AMM_DERIVE_USER_ID'])
        return user_id_set

    @classmethod
    @cached(1800)
    def get_market_trade_map(cls):
        model = SpotAssetTradeSummary
        # 优先取最新的报表，但是报表生成时间有延迟，会导致0-2点那个小时的统计数据不完整
        # 产品说可以补充往前一天的报表
        query_date = yesterday() - timedelta(1)
        rows = model.query.filter(
            model.report_date >= query_date
        ).with_entities(
            model.market,
            model.trade_amount
        ).order_by(
            model.report_date
        )
        # 时间按正序排序，字典中新的数据会覆盖老的
        return {i.market: i.trade_amount for i in rows}

    @classmethod
    @cached(1800)
    def get_market_quote_asset_map(cls):
        market_quote_asset_map = dict()
        markets_detail = MarketCache.online_markets_detail()
        for market, detail in markets_detail.items():
            market_quote_asset_map[market] = detail["quote_asset"]
        return market_quote_asset_map

    @classmethod
    def send_all_asset_alert(cls):
        asset_market_map = defaultdict(list)
        spot_risk_conf = RiskControlGroupConfig().spot_big_booking_order

        markets_detail = MarketCache.online_markets_detail()
        for market, detail in markets_detail.items():
            asset_market_map[detail["base_asset"]].append(market)

        # 币种市场
        for asset, market_list in asset_market_map.items():
            alert_asset_notice.delay(asset, market_list, spot_risk_conf)
            time.sleep(0.5)

    @classmethod
    def calc_market_amount_usd(cls, orders, market_quote_asset_map, prices):
        market_amount_usd = defaultdict(Decimal)
        market_amount = defaultdict(Decimal)
        for order in orders:
            quote_asset = market_quote_asset_map[order["market"]]
            amount_usd = Decimal(order["amount"]) * Decimal(order["price"]) * Decimal(prices[quote_asset])
            market_amount_usd[order['market']] += amount_usd
            market_amount[order['market']] += Decimal(order["amount"])
        return market_amount, market_amount_usd

    @classmethod
    def send_notice_and_add_risk(cls, user_id, asset, side, sum_amount, market_amount, market_amount_usd, threshold):
        url = config["ADMIN_CONTACTS"]['big_booking_deposit_notice']
        detail = [f'{k}挂单: {amount_to_str(v, 2)} USD, 数量为 {market_amount[k]}' for k, v in market_amount_usd.items()]
        sum_amount_str = amount_to_str(sum_amount, 2)
        content_detail = '\n'.join(detail)
        content = f"告警类型：单用户单个币种当前累计挂单监控\n" \
                  f"用户ID：{user_id}\n" \
                  f"触发币种：{asset}\n" \
                  f"挂单方向：{cls.side_map[side]}\n" \
                  f"挂单市值：{sum_amount_str} USD / {threshold} USD（阈值）\n" \
                  f"挂单详情: \n" \
                  f"{content_detail}"
        # 产品要求24小时内只有一次告警和风控记录。
        is_send = send_alert_notice(
            content=content,
            url=url,
            expired_seconds=cls.timeout,
            msg_id=cls.msg_id_sf.format(side, asset, user_id),
        )
        if is_send:
            risk_detail = f"用户单币种当前累计挂单监控告警，{asset} 挂单市值 {sum_amount_str} USD, {', '.join(detail)}"
            add_risk_user(user_id, RiskUser.Reason.USER_ASSET_PENDING_ORDER_THRESHOLD, risk_detail, source=asset)
        return is_send


@celery_task(queue=CeleryQueues.RISK_CONTROL)
@lock_call(with_args=["asset"])
def alert_asset_notice(asset, market_list, spot_risk_conf):
    cls = SpotBigBookingOrderMonitor
    maker_set = cls.get_ignore_user_set()
    prices = PriceManager.assets_to_usd()
    market_trade_map = cls.get_market_trade_map()
    market_quote_asset_map = cls.get_market_quote_asset_map()
    yesterday_asset_amount = sum([Decimal(market_trade_map.get(i, 0)) for i in market_list])
    price_offset_map = {
        market: cls.get_config_value(market_trade_map.get(market, 0), spot_risk_conf["booking_order_offset"])
        for market in market_list
    }
    # 买卖方向
    for side in list(OrderSideType):
        main_user_data = get_main_user_order_data(market_list, price_offset_map, side)
        for user_id, orders in main_user_data.items():
            if user_id in maker_set:
                continue
            market_amount, market_amount_usd = cls.calc_market_amount_usd(orders, market_quote_asset_map, prices)
            threshold = cls.get_config_value(yesterday_asset_amount, spot_risk_conf["asset_order_threshold"])
            sum_amount = sum(market_amount_usd.values())
            if sum_amount > threshold:
                asset_amount = sum([Decimal(o['amount']) for o in orders])
                # 产品要求同一个币种，如果挂单数量没有变化，只触发过一次告警
                cache = SpotBigBookingCache(user_id, asset, side)
                cache_amount = cache.read_one()
                if cache_amount != asset_amount:
                    cache.set_one(asset_amount)
                    cls.send_notice_and_add_risk(
                        user_id, asset, side, sum_amount, market_amount, market_amount_usd, threshold)


class FiatThirdPartyUseMonitor:
    expired_seconds = 86400 * 15

    @classmethod
    def send_notice(cls, third_party: str):
        alert_text = f"服务商 {third_party} 近15天没有新的成功订单, 请检查订单情况并与第三方服务商沟通" \
                     f" <@U03SWPXLVLJ><@U06QNMUV807><@U09L80UL5C4> "
        url = config["ADMIN_CONTACTS"]['fiat_notice']
        # url = "*********************************************************************************"

        send_alert_notice(
            content=alert_text,
            url=url,
            expired_seconds=cls.expired_seconds,
            msg_id=f'{third_party}_notice',
        )

    @classmethod
    def check_fiat_third_party_order(cls):
        _today = today()
        for third_party in get_fiat_partners():
            model = FiatOrder
            row: FiatOrder = model.query.filter(
                model.third_party == third_party,
                model.status == model.StatusType.APPROVED
            ).order_by(
                model.created_at.desc()
            ).first()
            if row:
                interval = (_today - row.created_at.date()).days + 1
                if interval > 15:
                    cls.send_notice(third_party)
            else:
                cls.send_notice(third_party)
