# -*- coding: utf-8 -*-
from decimal import Decimal, ROUND_UP
from functools import cached_property
from typing import NamedTuple

from flask import current_app

from app.common import OrderSideType, OrderOption, PrecisionEnum, CeleryQueues
from app.business.order import Order
from app.models import Market
from app.models.payment import (
    db, AssetFixedExchangeSysUser, SysAssetFixedExchangeHistory, SysAssetFixedExchangeOrderHistory,
)
from app.business import (
    SPOT_ACCOUNT_ID, SYSTEM_ORDER_SOURCE, ServerClient, LockKeys, CacheLock, SiteSettings,
    PriceManager, lock_call
)
from app.caches import MarketCache
from app.utils import quantize_amount, amount_to_str, now, celery_task, route_module_to_celery_queue


route_module_to_celery_queue(__name__, CeleryQueues.PAYMENT)


class _ExchangeParam(NamedTuple):
    # 兑换挂单参数
    max_deviation: Decimal  # 最大价格偏差
    inc_deviation: Decimal  # 每次递增偏差
    inc_period: Decimal  # 递增时间间隔 秒


PAYMENT_HEDGING_PARAM = _ExchangeParam(
    max_deviation=Decimal("0.05"),
    inc_deviation=Decimal("0.002"),
    inc_period=Decimal("10"),
)


class SysAssetFixedExchanger:
    """Fixed兑换逻辑"""

    MID_ASSET = "USDT"  # 跨市场时只通过usdt
    MIN_EXCHANGE_USD = Decimal("0.01")  # 低于此市值可能不再兑换

    def __init__(self, exchange_id: int):
        self.exchange_id: int = exchange_id
        self.exchange_info: SysAssetFixedExchangeHistory = SysAssetFixedExchangeHistory.query.get(exchange_id)
        self.sys_user_id = self.exchange_info.sys_user_id
        self.exchange_param = self.get_exchange_param()
        self.client = ServerClient()
        self._market_info_dict: dict[str, dict] = {}
        self.buy_price_deviation_ratio = Decimal()  # 买单价格递增偏差-修正参数

    @classmethod
    def get_or_create_exchange_his(
        cls,
        biz_type: SysAssetFixedExchangeHistory.BizType,
        biz_id: int,
        source_asset: str,
        source_amount: Decimal,
        target_asset: str,
        target_amount: Decimal,
    ) -> SysAssetFixedExchangeHistory:
        """创建一笔兑换记录，分配系统子账号并创建兑换记录"""
        exc_his = SysAssetFixedExchangeHistory.query.filter(
            SysAssetFixedExchangeHistory.biz_id == biz_id,
            SysAssetFixedExchangeHistory.biz_type == biz_type,
        ).first()
        if exc_his:
            return exc_his

        with CacheLock(key=LockKeys.fixed_exchange_sys_user(), wait=False):
            db.session.rollback()

            # 分配可用的系统子账号
            sys_user: AssetFixedExchangeSysUser = AssetFixedExchangeSysUser.query.filter(
                AssetFixedExchangeSysUser.status == AssetFixedExchangeSysUser.Status.USABLE,
            ).order_by(AssetFixedExchangeSysUser.id.asc()).first()
            if not sys_user:
                raise RuntimeError(f"SysAssetFixedExchangeHistory no usable sys_user for {biz_type.name} {biz_id}")

            # 更新系统子账号为不可用
            sys_user.status = AssetFixedExchangeSysUser.Status.UNUSABLE

            # 创建兑换记录
            exc_his = SysAssetFixedExchangeHistory(
                biz_type=biz_type,
                biz_id=biz_id,
                sys_user_id=sys_user.user_id,
                source_asset_data={source_asset: source_amount},
                target_asset_data={target_asset: target_amount},
            )
            db.session.add(exc_his)
            db.session.commit()

            return exc_his

    @classmethod
    def free_sys_user(cls, sys_user_id: int):
        """释放系统子账号，外面commit"""
        sys_user: AssetFixedExchangeSysUser = AssetFixedExchangeSysUser.query.filter(
            AssetFixedExchangeSysUser.user_id == sys_user_id,
        ).first()
        assert sys_user.status == AssetFixedExchangeSysUser.Status.UNUSABLE
        sys_user.status = AssetFixedExchangeSysUser.Status.USABLE

    def get_exchange_param(self) -> _ExchangeParam:
        if self.exchange_info.biz_type == SysAssetFixedExchangeHistory.BizType.PAYMENT_HEDGING:
            return PAYMENT_HEDGING_PARAM
        raise

    def get_market_info(self, market: str) -> dict:
        if market not in self._market_info_dict:
            info = MarketCache(market).dict
            self._market_info_dict[market] = info
        return self._market_info_dict[market]

    @cached_property
    def all_market_index_price_map(self) -> dict[str, Decimal]:
        result = self.client.get_all_indices()
        return result

    @cached_property
    def all_market_last_price_map(self) -> dict[str, Decimal]:
        tickers = self.client.get_all_market_tickers()
        return {m: ticker['last'] for m, ticker in tickers.items()}

    def get_market_price(self, market: str) -> Decimal:
        """获取市场价格，优先用指数价，没有指数价格时用市场价"""
        if market in self.all_market_index_price_map:
            return Decimal(self.all_market_index_price_map[market])
        if market in self.all_market_last_price_map:
            return Decimal(self.all_market_last_price_map[market])
        raise ValueError(f"{market} not index_or_last_price")

    def get_sys_user_balances(self) -> dict[str, Decimal]:
        balances = self.client.get_user_balances(self.sys_user_id, account_id=SPOT_ACCOUNT_ID)
        result_balances = {
            asset: available
            for asset, balance_info in balances.items()
            if (available := (balance_info["available"])) > Decimal()
        }
        return result_balances

    def get_balance_dump_assets(self) -> set[str]:
        dp_assets = set(self.exchange_info.source_asset_data) | set(self.exchange_info.target_asset_data) | {self.MID_ASSET}
        return dp_assets

    def set_start_info(self) -> None:
        if not self.exchange_info.started_at:
            balances = self.get_sys_user_balances()
            dump_assets = self.get_balance_dump_assets()
            balances = {k: v for k, v in balances.items() if k in dump_assets}
            self.exchange_info.started_balance_data = balances
            self.exchange_info.started_at = now()
            db.session.commit()

    def set_finish_info(self) -> None:
        if not self.exchange_info.finished_at:
            balances = self.get_sys_user_balances()
            dump_assets = self.get_balance_dump_assets()
            balances = {k: v for k, v in balances.items() if k in dump_assets}
            self.exchange_info.finished_balance_data = balances
            self.exchange_info.status = SysAssetFixedExchangeHistory.Status.FINISHED
            self.exchange_info.finished_at = now()
            db.session.commit()

    def stop_exchange(self) -> None:
        """停止兑换"""
        self.set_finish_info()
        self.on_finished_notify()

    def on_finished_notify(self):
        from app.business.payment.hedging import process_payment_hedging_his_task

        if self.exchange_info.biz_type == SysAssetFixedExchangeHistory.BizType.PAYMENT_HEDGING:
            process_payment_hedging_his_task.delay(self.exchange_info.biz_id)

    def execute(self) -> None:
        """执行兑换操作"""
        if self.exchange_info.status != SysAssetFixedExchangeHistory.Status.EXCHANGING:
            return
        if not (SiteSettings.spot_trading_enabled and SiteSettings.trading_enabled):
            current_app.logger.warning("Trading is disabled, skipping asset exchange execution")
            return

        self.cancel_all_order()
        self.set_start_info()

        # 计算剩余需要兑换的资产
        balances = self.get_sys_user_balances()
        source_asset_data = {k: Decimal(v) for k, v in self.exchange_info.source_asset_data.items()}
        target_asset_data = {k: Decimal(v) for k, v in self.exchange_info.target_asset_data.items()}
        used_source_data, filled_target_data, _ = self.exchange_info.get_asset_exchanged_data(balances)

        remain_source_amount_map = {}  # 剩余可用于交易的原始币种数量
        for s_asset, s_amount in source_asset_data.items():
            used_s_amount = used_source_data.get(s_asset, Decimal())
            remain_s_amount = s_amount - used_s_amount
            if remain_s_amount > 0:
                remain_source_amount_map[s_asset] = remain_s_amount
        need_target_amount_map = {}  # 还需要的目标币种数量
        for t_asset, t_amount in target_asset_data.items():
            filled_t_amount = filled_target_data.get(t_asset, Decimal())
            need_t_amount = t_amount - filled_t_amount
            if need_t_amount > 0:
                need_target_amount_map[t_asset] = need_t_amount

        if not remain_source_amount_map or not need_target_amount_map:
            # 兑换币种用完了 或 目标币种买够了，则停止交易
            self.stop_exchange()
            return

        # 计算可卖出的资产
        sell_asset_amount_map = {k: min(v, balances.get(k, Decimal())) for k, v in remain_source_amount_map.items()}
        if self.MID_ASSET not in self.exchange_info.target_asset_data and self.MID_ASSET not in sell_asset_amount_map:
            # 如果中间币种USDT不是目标资产，也不是源资产，则添加USDT到待交易币种中
            _cur_bl = quantize_amount(balances.get(self.MID_ASSET, Decimal()), 8)
            _start_bl = quantize_amount(Decimal(self.exchange_info.started_balance_data.get(self.MID_ASSET, Decimal())), 8, ROUND_UP)
            _mid_can_sell_amount = _cur_bl - _start_bl
            # 不允许多用，中间币种余额可能是之前兑换记录留下来的
            if _mid_can_sell_amount > 0:
                sell_asset_amount_map[self.MID_ASSET] = _mid_can_sell_amount

        min_b = Decimal(10) ** -PrecisionEnum.COIN_PLACES
        if not any(v > min_b for v in sell_asset_amount_map.values()):
            # 剩余可卖出的币种数目太少了
            self.stop_exchange()
            return

        # 构建订单参数
        order_params = []
        for target_asset, target_amount in need_target_amount_map.items():
            self.buy_price_deviation_ratio = self.calc_buy_price_deviation_ratio(sell_asset_amount_map, target_asset, target_amount)
            order_params = self.build_order_params(sell_asset_amount_map, target_asset, target_amount)
            if order_params:
                # 每次只买一个目标币种
                break

        if not order_params:
            # 所有的need_target_map，都无法挂单。说明是 无可用市场 或者 amount为0
            # 当剩余市值太小时不再兑换（amount为0时）
            asset_price_dict = PriceManager.assets_to_usd(list(sell_asset_amount_map))
            remain_usd = sum([asset_price_dict[a] * m for a, m in sell_asset_amount_map.items()])
            if remain_usd <= self.MIN_EXCHANGE_USD:
                self.stop_exchange()
            else:
                current_app.logger.error(
                    f"SysAssetFixedExchanger biz_type: {self.exchange_info.biz_type.name} "
                    f"biz_id: {self.exchange_info.biz_id} exc_his: {self.exchange_id} order_params empty"
                )
            return

        for order_param in order_params:
            try:
                self.put_order(order_param)
            except Exception as e:
                current_app.logger.error(
                    f"SysAssetFixedExchanger biz_type: {self.exchange_info.biz_type.name} "
                    f"biz_id: {self.exchange_info.biz_id} exc_his: {self.exchange_id} "
                    f"put_order failed. order_param: {order_param} error: {e!r}"
                )

    def calc_buy_price_deviation_ratio(
        self,
        sell_asset_amount_map: dict[str, Decimal],
        target_asset: str,
        target_amount: Decimal,
    ) -> Decimal:
        """计算买单价格偏差的修正系数ratio"""
        mid_asset = self.MID_ASSET
        if target_asset == mid_asset:
            return Decimal("1")
        if mid_asset not in sell_asset_amount_map:
            return Decimal()

        assets = set(sell_asset_amount_map)
        assets.add(target_asset)
        asset_price_dict = PriceManager.assets_to_usd(assets)
        target_usd = target_amount * asset_price_dict[target_asset]
        can_buy_usd = sell_asset_amount_map[mid_asset] * asset_price_dict[mid_asset]

        # 当USDT越多，价格越接近正常偏差；当USDT越少，价格越平滑
        ratio_b = min(max(can_buy_usd / target_usd, Decimal()), Decimal("1"))
        ratio_b = quantize_amount(ratio_b, 2)
        return ratio_b

    def build_order_params(
        self,
        sell_asset_amount_map: dict[str, Decimal],
        target_asset: str,
        target_amount: Decimal,
    ) -> list[dict]:
        """构建订单参数"""
        mid_asset = self.MID_ASSET
        online_markets = MarketCache.list_online_markets()
        mid_temp_amount = sell_asset_amount_map.get(mid_asset, Decimal())
        order_params = []
        for asset, asset_balance in sell_asset_amount_map.items():
            if (m0 := f"{asset}{mid_asset}") in online_markets and f"{target_asset}{mid_asset}" in online_markets:
                # ETHBTC这种市场不直接兑换，优先通过 ETHUSDT、BTCUSDT去兑换
                market = m0
                is_direct = False
            elif (m1 := f"{asset}{target_asset}") in online_markets:
                market = m1
                is_direct = True
            elif (m2 := f"{target_asset}{asset}") in online_markets:
                market = m2
                is_direct = True
            else:
                current_app.logger.error(
                    f"SysAssetFixedExchanger.build_order_params exc_his: {self.exchange_id} "
                    f"asset: {asset} {target_asset} not found trade market"
                )
                continue

            market_info = self.get_market_info(market)
            if not market_info or market_info["status"] != Market.Status.ONLINE or market_info["trading_disabled"]:
                current_app.logger.error(
                    f"SysAssetFixedExchanger.build_order_params exc_his: {self.exchange_id} "
                    f"asset: {asset} {target_asset} market: {market} status invalid"
                )
                continue

            if is_direct:
                # asset -> target_asset
                side, price, amount, deviation = self.build_market_order_param(
                    market, asset, asset_balance, target_asset, target_amount
                )
                delta_pay_amount = amount if side == OrderSideType.BUY else price * amount
                delta_pay_amount = quantize_amount(delta_pay_amount, PrecisionEnum.COIN_PLACES)  # 向下取整
            else:
                # asset -> usdt -> target_asset 一定是先卖出 再买入
                # 先用需要买入loan_asset的数目，算出要支付的usdt数目
                m2 = f"{target_asset}{mid_asset}"
                m2_price, _ = self.calc_price_and_deviation(m2, OrderSideType.BUY)
                m2_need_mid_amount = m2_price * target_amount
                m2_need_mid_amount = quantize_amount(m2_need_mid_amount, PrecisionEnum.COIN_PLACES, ROUND_UP)
                # 减去已有的usdt数目
                m2_need_mid_dealt_amount = m2_need_mid_amount - mid_temp_amount
                if m2_need_mid_dealt_amount <= Decimal():
                    # 已有的usdt已经足够
                    continue
                mid_temp_amount += m2_need_mid_dealt_amount
                # 再用需要得到的usdt数目，算出要卖出的币种asset的数目
                side, price, amount, deviation = self.build_market_order_param(
                    market, asset, asset_balance, mid_asset, m2_need_mid_dealt_amount
                )
                assert side == OrderSideType.SELL
                # 全卖了可能也不够，重新算下能够得到多少U，这些U能够得到买入多少借币币种
                delta_pay_amount = quantize_amount(price * amount / m2_price, PrecisionEnum.COIN_PLACES)  # 向下取整
            if amount > 0:
                order_params.append(
                    {
                        "market": market,
                        "side": side,
                        "price": price,
                        "amount": amount,
                        "deviation": deviation,
                    }
                )
                target_amount -= delta_pay_amount
            if target_amount <= 0:
                break
        return order_params

    def build_market_order_param(
        self,
        market: str,
        source_asset: str,
        source_amount: Decimal,
        want_asset: str,
        want_amount: Decimal,
    ) -> tuple[OrderSideType, Decimal, Decimal, Decimal]:
        market_info = self.get_market_info(market)
        if source_asset == market_info["quote_asset"] and want_asset == market_info["base_asset"]:
            side = OrderSideType.BUY
        elif source_asset == market_info["base_asset"] and want_asset == market_info["quote_asset"]:
            side = OrderSideType.SELL
        else:
            raise ValueError(f"build_market_order_param source:{source_asset} want:{want_asset} in market:{market}")

        price, deviation = self.calc_price_and_deviation(market, side)
        if side == OrderSideType.BUY:
            # usdt -> btc, 向下取整 不能超过余额
            can_buy_amount = quantize_amount(source_amount / price, PrecisionEnum.COIN_PLACES)
            amount = min(can_buy_amount, want_amount)
        else:
            # btc -> usdt，向上取整，尽量保证成交后得到的会更多
            precision = market_info["base_asset_precision"]
            need_sell_amount = quantize_amount(want_amount / price, precision, ROUND_UP)
            amount = min(source_amount, need_sell_amount)
        amount = quantize_amount(amount, PrecisionEnum.COIN_PLACES)
        return side, price, amount, deviation

    def calc_price_and_deviation(self, market: str, side: OrderSideType) -> tuple[Decimal, Decimal]:
        """计算价格和偏差"""
        index_price = self.get_market_price(market)
        if side == OrderSideType.BUY:
            price, v = self.calc_buy_price_and_deviation(market, index_price)
        else:
            price, v = self.calc_sell_price_and_deviation(market, index_price)
        price = quantize_amount(price, self.get_market_info(market)['quote_asset_precision'])
        return price, v

    def get_market_order_last_price_deviation(self, market: str, side: OrderSideType) -> Decimal:
        """获取市场订单最后价格偏差"""
        last_order_row: SysAssetFixedExchangeOrderHistory = SysAssetFixedExchangeOrderHistory.query.filter(
            SysAssetFixedExchangeOrderHistory.exchange_id == self.exchange_id,
            SysAssetFixedExchangeOrderHistory.market == market,
            SysAssetFixedExchangeOrderHistory.side == side.value,
        ).order_by(
            SysAssetFixedExchangeOrderHistory.id.desc(),
        ).with_entities(
            SysAssetFixedExchangeOrderHistory.price_deviation,
        ).first()
        price_deviation = last_order_row.price_deviation if last_order_row else Decimal()
        return price_deviation

    def calc_sell_price_and_deviation(self, market: str, index_price: Decimal) -> tuple[Decimal, Decimal]:
        """计算卖单价格和偏差"""
        # 卖出价格 = 最新价 * (1- 价格偏差) ，价格偏差=Min(时长 * 基本偏差，最大偏差)
        price_deviation = self.get_market_order_last_price_deviation(market, OrderSideType.SELL)
        deviation = price_deviation + self.exchange_param.inc_deviation
        deviation = quantize_amount(min(deviation, self.exchange_param.max_deviation), 8)
        return index_price * (1 - deviation), deviation

    def calc_buy_price_and_deviation(self, market: str, index_price: Decimal) -> tuple[Decimal, Decimal]:
        """计算买单价格和偏差"""
        # 买入价格 = 最新价 * (1+价格偏差) ，价格偏差=Min(上次买入价格偏差 + 基本偏差 * ratio，最大偏差)
        price_deviation = self.get_market_order_last_price_deviation(market, OrderSideType.BUY)
        deviation = price_deviation + self.exchange_param.inc_deviation * self.buy_price_deviation_ratio
        deviation = quantize_amount(min(deviation, self.exchange_param.max_deviation), 8)
        return index_price * (1 + deviation), deviation

    def cancel_all_order(self) -> None:
        all_orders = []
        page = 1
        while True:
            page_orders = self.client.user_pending_orders(
                self.sys_user_id, account_id=SPOT_ACCOUNT_ID, limit=100, page=page
            )
            all_orders.extend(page_orders)
            page += 1
            if not page_orders.has_next:
                break
        markets = {o["market"] for o in all_orders}
        for m in markets:
            self.client.cancel_user_all_order(
                user_id=self.sys_user_id,
                account_id=SPOT_ACCOUNT_ID,
                market=m,
                source_option=SYSTEM_ORDER_SOURCE,
            )

    def put_order(self, param: dict) -> None:
        """下订单（不收手续费）"""
        market = param["market"]
        market_info = self.get_market_info(market)
        side = param["side"].value
        amount = amount_to_str(param["amount"], market_info["base_asset_precision"])
        price = amount_to_str(param["price"], market_info["quote_asset_precision"])
        order_info = self.client.put_limit_order(
            user_id=self.sys_user_id,
            account_id=SPOT_ACCOUNT_ID,
            market=market,
            side=side,
            amount=amount,
            price=price,
            taker_fee_rate="0",
            maker_fee_rate="0",
            source=Order.OrderSourceType.SYSTEM.value,
            fee_asset=None,
            fee_discount="0",
            option=OrderOption.HIDE | OrderOption.WITHOUT_ORDER_MIN_AMOUNT | OrderOption.SYSTEM,
        )
        order_his = SysAssetFixedExchangeOrderHistory(
            exchange_id=self.exchange_id,
            market=market,
            order_id=order_info["id"],
            side=side,
            type=Order.NormalOrderType.LIMIT.value,
            price=price,
            amount=amount,
            price_deviation=param["deviation"],
        )
        db.session.add(order_his)
        db.session.commit()


@celery_task
@lock_call(with_args=True)
def execute_sys_asset_fixed_exchange_task(exchange_id: int):
    """执行fixed资产兑换"""
    SysAssetFixedExchanger(exchange_id).execute()
