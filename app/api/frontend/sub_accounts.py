# -*- coding: utf-8 -*-
import json
import math
import re
from collections import defaultdict
from datetime import timed<PERSON>ta
from decimal import Decimal, ROUND_DOWN
from functools import partial
from typing import List, Dict

from flask import g, send_file
from flask_babel import gettext as _
from sqlalchemy import func
from sqlalchemy.orm import aliased
from webargs import fields

from app.business.admin_tag import TagType
from app.exceptions.legacy import OperationNotAllowed
from app.models.admin_tag import AdminTagUser
from app.models.custody import CustodyPlatform, CustodySubAccount

from ..common import (
    Resource, Namespace, respond_with_code, require_login,
    require_2fa, get_request_ip
)
from ..common.fields import (
    PageField, LimitField, EnumField, AssetField,
    TimestampField, PositiveDecimalField, EmailField
)
from ..common.request import require_user_request_permission
from ...business import (
    ServerClient,
    PerpetualServerClient,
    SubAccountManager,
    UserPreferences,
    UserSettings,
    <PERSON>acheLock,
    <PERSON><PERSON><PERSON><PERSON>,
    SPOT_ACCOUNT_ID,
    <PERSON><PERSON><PERSON><PERSON>,
    get_user_using_coupon_balance,
)
from ...business.credit import update_credit_user_risk_record_task
from ...business.email import send_sub_account_manage_notice_email
from ...business.sub_account import update_sub_account_balance_cache_task, get_sub_account_balance_map, \
    execute_subuser_clean_log_task
from ...caches import ApiAuthCache, PerpetualMarketCache, SubAccountInfoCache, PerpetualCoinTypeCache
from ...caches.user import SubMainUserCache
from ...caches.user import UserVisitPermissionCache, UserConfigKeyCache
from ...common import PrecisionEnum, MobileCodeType, SubAccountPermission, SUB_ACCOUNT_MANAGER_NUM_LIMIT
from ...config import config
from ...exceptions import (
    InvalidArgument,
    InsufficientBalance,
    SubAccountRelationError,
    SubAccountUsernameExists,
    TransferNotAllowed,
    SubAccountBeyondAmountLimit,
    EditManagedSubAccountName,
    SubAccountManagerBeyondAmountLimit,
    TransferOutNotAllowed,
)
from ...models import SubAccount, User, ApiAuth, db, SubAccountManagerRelation, SubAccountAssetTransfer, \
    AssetInvestmentConfig, UserLiquidity, SubAccountClearLog, SubAccountManagerRelationHistory
from ...utils import datetime_to_str, timestamp_to_datetime, now, GeoIP, ExcelExporter, quantize_amount, batch_iter

ns = Namespace('Sub Accounts')
url_prefix = '/sub_accounts'


RE_NAME = re.compile(r'^[A-Za-z0-9_-]+$')


def new_sub_user_preference(sub_user_id: int, main_user_id: int):
    main_pref = UserPreferences(main_user_id)
    sub_pref = UserPreferences(sub_user_id)
    sub_pref.language = main_pref.language
    sub_pref.timezone_offset = main_pref.timezone_offset
    sub_pref.currency = main_pref.currency
    sub_pref.cet_discount_enabled = main_pref.cet_discount_enabled
    sub_pref.order_confirmation = main_pref.order_confirmation
    sub_pref.perpetual_order_confirmation = main_pref.perpetual_order_confirmation


@ns.route('/')
@respond_with_code
class SubAccountsResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            user_name=fields.String,
            status=EnumField(SubAccount.Status),
            sort_type=EnumField(["balance_usd_asc", "balance_usd_desc", "create_time_asc", "create_time_desc"]),
            filter_balance=fields.Bool,
        )
    )
    def get(cls, **kwargs):
        """ 子账号列表(包括 用户创建的、系统策略子帐号，系统策略子帐号会汇总为一个) """
        main_user = g.user
        page, limit = kwargs["page"], kwargs["limit"]
        sort_type = kwargs.get("sort_type")
        q = SubAccount.query.filter(
            SubAccount.main_user_id == main_user.id,
            SubAccount.is_visible.is_(True),
        )
        if user_name := kwargs.get('user_name'):
            sub_acc = SubAccountManager.get_sub_account_by_name(main_user.id, user_name, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES)
            if not sub_acc:
                return dict(
                    has_next=False,
                    curr_page=1,
                    count=0,
                    data=[],
                    total=0,
                    total_page=1,
                )
            else:
                q = q.filter(SubAccount.id == sub_acc.id)
        if status := kwargs.get("status"):
            q = q.filter(SubAccount.status == status)

        rows = q.order_by(SubAccount.id.desc()).all()  # 查全部 大部分用户的子账号数目限制为50个
        sub_balance_map = get_sub_account_balance_map(main_user.id)
        if kwargs.get("filter_balance"):
            rows = [i for i in rows if sub_balance_map.get(i.user_id, Decimal()) > Decimal()]

        all_items = []
        sty_sub_usd_map = {}
        copy_sub_usd_map = {}
        for row in rows:
            if row.type not in SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES:
                if row.type == SubAccount.Type.STRATEGY:
                    sty_sub_usd_map[row.user_id] = sub_balance_map.get(row.user_id, Decimal())
                elif row.type in [SubAccount.Type.COPY_TRADER, SubAccount.Type.COPY_FOLLOWER]:
                    copy_sub_usd_map[row.user_id] = sub_balance_map.get(row.user_id, Decimal())
                continue
            item = dict(
                user_id=row.user_id,
                remark=row.remark,
                status=row.status.name,
                permissions=[i.name for i in row.enum_permissions],
                manage_status=row.manage_status.name,
                balance_usd=sub_balance_map.get(row.user_id, Decimal()),
                type=row.type.name,
            )
            all_items.append(item)
        if sort_type == "create_time_asc":
            all_items.sort(key=lambda x: x["user_id"])
        elif sort_type == "balance_usd_asc":
            all_items.sort(key=lambda x: x["balance_usd"])
        elif sort_type == "balance_usd_desc":
            all_items.sort(key=lambda x: x["balance_usd"], reverse=True)

        if sty_sub_usd_map:
            # 把所有的策略子账号汇总为一个显示
            sty_item = dict(
                user_id=0,
                remark="",
                status=SubAccount.Status.VALID.name,
                permissions=[],
                manage_status=SubAccount.ManageStatus.UNMANAGED.name,
                balance_usd=sum(sty_sub_usd_map.values()),
                type=SubAccount.Type.STRATEGY.name,
            )
            all_items.insert(0, sty_item)
        if copy_sub_usd_map:
            copy_item = dict(
                user_id=0,
                remark="",
                status=SubAccount.Status.VALID.name,
                permissions=[],
                manage_status=SubAccount.ManageStatus.UNMANAGED.name,
                balance_usd=sum(copy_sub_usd_map.values()),
                type="COPY_TRADING",  # 没这个枚举
            )
            all_items.insert(0, copy_item)

        items = all_items
        sub_user_ids = [i["user_id"] for i in items]
        user_name_map = dict(User.query.filter(User.id.in_(sub_user_ids)).with_entities(User.id, User.name).all())
        disabled_subs = UserConfigKeyCache.get_login_disabled_by_admin_ids(sub_user_ids)
        custody_sub_users = CustodySubAccount.query.filter(
            CustodySubAccount.main_user_id == main_user.id,
        ).with_entities(
            CustodySubAccount.user_id,
            CustodySubAccount.platform,
        ).all()
        custody_platform_str_map = {i.user_id: i.platform.value for i in custody_sub_users}
        for i in items:
            i["custody_platform"] = custody_platform_str_map.get(i["user_id"], None)
            if i["type"] in [i.name for i in SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES]:
                i["user_name"] = user_name_map[i["user_id"]]
                i["switchable"] = i["user_id"] not in disabled_subs
            else:
                i["user_name"] = i["remark"] = ""
                i["permissions"] = []
                i["switchable"] = False  # 其他类型的子帐号都不允许登入

        total = len(all_items)
        return dict(
            has_next=total > page * limit,
            curr_page=page,
            count=len(items),
            data=items,
            total=total,
            total_page=math.ceil(total / limit),
        )


@ns.route('/total-balance')
@respond_with_code
class SubAccountTotalBalanceResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 子账号总资产USD """
        main_user = g.user
        sub_balance_map = get_sub_account_balance_map(main_user.id)
        total_balance_usd = sum(sub_balance_map.values())
        return {"total_balance_usd": total_balance_usd}


@ns.route("/names")
@respond_with_code
class SubAccountNamesResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            status=EnumField(SubAccount.Status),
        )
    )
    def get(cls, **kwargs):
        """ 子账号用户名列表 """
        main_user = g.user
        q = SubAccount.query.filter(
            SubAccount.main_user_id == main_user.id,
            SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
            SubAccount.is_visible.is_(True),
        )
        if status := kwargs.get("status"):
            q = q.filter(SubAccount.status == status)

        rows = q.order_by(SubAccount.id.desc()).all()
        sub_user_ids = [i.user_id for i in rows]
        sub_user_name_map = {}
        for chunk_user_ids in batch_iter(sub_user_ids, 500):
            sub_user_name_map.update(dict(User.query.filter(User.id.in_(chunk_user_ids)).with_entities(User.id, User.name).all()))

        items = []
        for row in rows:
            items.append(
                dict(
                    user_id=row.user_id,
                    user_name=sub_user_name_map[row.user_id],
                    status=row.status.name,
                    type=row.type.name,
                    manage_status=row.manage_status.name,
                )
            )
        return items


@ns.route('/register')
@respond_with_code
class SubAccountRegistrationResource(Resource):

    @classmethod
    @require_2fa(MobileCodeType.SUB_ACCOUNT_REGISTRATION, allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            user_name=fields.String(required=True),
            permissions=fields.List(EnumField(SubAccountPermission), required=True),
            remark=fields.String(missing=""),
            custody_platform=EnumField(CustodyPlatform, required=False),
        )
    )
    def post(cls, **kwargs):
        """ 新增子账号 """
        require_user_request_permission(g.user)
        main_user: User = g.user
        main_user_id = main_user.id
        with CacheLock(LockKeys.sub_account_created(main_user_id)):
            db.session.rollback()
            num_limit = SubAccountManager.get_user_sub_account_num_limit(main_user_id)
            sub_acc_count = SubAccount.query.filter(
                SubAccount.main_user_id == main_user.id,
                SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
            ).with_entities(func.count()).scalar() or 0
            if sub_acc_count >= num_limit:
                raise SubAccountBeyondAmountLimit(num=num_limit)

            user_name = kwargs['user_name']
            permissions = kwargs['permissions']
            permissions = sorted(set(permissions), key=permissions.index)

            if not 3 <= len(user_name) <= 26 or not RE_NAME.fullmatch(user_name):
                raise InvalidArgument

            if len(remark := kwargs['remark']) > 50:
                raise InvalidArgument

            if custody_platform := kwargs.get('custody_platform'):
                tag_user = AdminTagUser.query.filter(
                    AdminTagUser.user_id == main_user_id,
                    AdminTagUser.tag_id == TagType.CUSTODY_WHITELIST.value,
                    AdminTagUser.status == AdminTagUser.Status.PASSED
                ).first()
                if not tag_user:
                    raise InvalidArgument(message='custody platform not allowed')

            mu_model = aliased(User)
            if SubAccount.query \
                    .join(User, User.id == SubAccount.user_id) \
                    .join(mu_model, mu_model.id == SubAccount.main_user_id) \
                    .filter(mu_model.id == main_user_id,
                            User.name == user_name,
                            SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES)) \
                    .first() is not None:
                raise SubAccountUsernameExists

            user = db.session_add_and_commit(User(
                login_password_updated_at=now(),
                registration_ip=(ip := get_request_ip()),
                registration_location=GeoIP(ip).location,
                location_code=main_user.location_code,
                name=user_name,
                user_type=User.UserType.SUB_ACCOUNT,
            ))
            sub_account_permissions = json.dumps([i.name for i in permissions])
            sub_account_type = SubAccount.Type.NORMAL

            if custody_platform:
                # 资产托管类型子账号权限固定为API、币币、合约交易
                sub_account_permissions = json.dumps([SubAccountPermission.API.name, SubAccountPermission.PERPETUAL.name])
                sub_account_type = SubAccount.Type.CUSTODY
                db.session.add(CustodySubAccount(
                    main_user_id=main_user_id,
                    user_id=user.id,
                    platform=custody_platform.name,
                ))

            db.session_add_and_commit(SubAccount(
                user_id=user.id,
                main_user_id=main_user.id,
                remark=remark,
                permissions=sub_account_permissions,
                type=sub_account_type,
            ))

            new_sub_user_preference(user.id, main_user.id)
            update_credit_user_risk_record_task.delay(main_user.id)
            SubAccountInfoCache(user.id).delete()
            SubMainUserCache.add_sub_user(user.id, main_user.id)

            return dict(
                user_id=user.id
            )


@ns.route('/balance/transfer')
@respond_with_code
class BalanceTransferResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            sub_user_id=fields.Integer(required=True),
            asset=fields.String(required=True),
            account_type=EnumField(["SPOT", "PERPETUAL"], required=True),
        )
    )
    def get(cls, **kwargs):
        """ 获取子账号可划转数量（现货、合约） """
        main_user: User = g.user
        sub_user_id = kwargs["sub_user_id"]
        asset = kwargs["asset"]
        if not SubAccountManager.has_relationship(main_user.id, sub_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError

        if kwargs["account_type"] == "SPOT":
            res = ServerClient().get_user_balances(sub_user_id, asset)
            transfer_amount = Decimal(res.get(asset, {}).get("available", "0"))
        else:
            res = PerpetualServerClient().get_user_balances(sub_user_id, asset)
            transfer_amount = Decimal(res.get(asset, {}).get("transfer", "0"))
            using_coupon_balance = get_user_using_coupon_balance(sub_user_id, asset)
            transfer_amount = transfer_amount - using_coupon_balance
        transfer_amount = quantize_amount(max(transfer_amount, Decimal()), 8)
        return dict(transfer_amount=transfer_amount)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            source_user_id=fields.Integer(required=True),
            target_user_id=fields.Integer(required=True),
            source_account=EnumField(SubAccountAssetTransfer.AccountType, required=True),
            target_account=EnumField(SubAccountAssetTransfer.AccountType, required=True),
            coin_type=AssetField(required=True),
            amount=PositiveDecimalField(places=PrecisionEnum.COIN_PLACES, rounding=ROUND_DOWN, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 子账号用户维度 划转 """
        main_user: User = g.user
        source_user_id = kwargs["source_user_id"]
        target_user_id = kwargs["target_user_id"]
        source_account_type = kwargs["source_account"]
        target_account_type = kwargs["target_account"]
        asset = kwargs["coin_type"]
        amount = kwargs["amount"]
        perpetual_account = SubAccountAssetTransfer.AccountType.PERPETUAL

        if source_user_id == target_user_id:
            raise InvalidArgument

        if main_user.id == source_user_id or (
                main_user.id != source_user_id and main_user.id != target_user_id
        ):
            require_user_request_permission(main_user)

        if main_user.id != source_user_id and main_user.id != target_user_id:
            # 子账号A 和 子账号B 的划转，只允许 现货账户 的划转
            if source_account_type == perpetual_account or target_account_type == perpetual_account:
                raise InvalidArgument

        if main_user.id != source_user_id:
            source_sub_acc = SubAccountManager.get_valid_sub_account(
                main_user.id, source_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
            if not source_sub_acc:
                raise InvalidArgument
        if main_user.id != target_user_id:
            target_sub_acc = SubAccountManager.get_valid_sub_account(
                main_user.id, target_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
            if not target_sub_acc:
                raise InvalidArgument
            if target_account_type == perpetual_account:
                if not target_sub_acc.has_permission(SubAccountPermission.PERPETUAL):
                    raise InvalidArgument(message=_("%(name)s无合约交易权限，不可向合约账户转入资金", name=target_sub_acc.user.name))
        if source_account_type == perpetual_account or target_account_type == perpetual_account:
            if asset not in set(PerpetualCoinTypeCache().read_aside()):
                raise InvalidArgument

        source_user_settings = UserSettings(source_user_id)
        if not source_user_settings.sub_account_transfer_enabled or not UserSettings(target_user_id).sub_account_transfer_enabled:
            raise TransferNotAllowed
        if source_user_settings.sub_account_transfer_out_disabled:
            raise TransferOutNotAllowed
        if source_account_type == perpetual_account and not source_user_settings.perpetual_transfer_out_enabled:
            raise TransferOutNotAllowed

        if source_account_type == perpetual_account:
            balances = PerpetualServerClient().get_user_balances(source_user_id, asset)
            transfer_amount = Decimal(balances.get(asset, {}).get("transfer", "0"))
            using_coupon_amount = get_user_using_coupon_balance(source_user_id, asset)
            if transfer_amount - using_coupon_amount < amount:
                raise InsufficientBalance
        else:
            balance = ServerClient().get_user_balances(source_user_id, asset)[asset]
            if balance["available"] < amount:
                raise InsufficientBalance

        with CacheLock(LockKeys.sub_account_transfer(source_user_id, target_user_id)):
            db.session.rollback()
            SubAccountManager.transfer_asset(
                main_user_id=main_user.id,
                source_user_id=source_user_id,
                source_account_type=source_account_type,
                target_user_id=target_user_id,
                target_account_type=target_account_type,
                asset=asset,
                amount=amount,
            )

        sub_user_ids = {source_user_id, target_user_id} - {main_user.id}
        update_sub_account_balance_cache_task.delay(main_user.id, list(sub_user_ids))

        return {}


@ns.route('/balance/transfer/history')
@respond_with_code
class TransferHistoryResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(dict(
        source_user_id=fields.Integer,
        target_user_id=fields.Integer,
        asset=fields.String,
        start_date=TimestampField(to_date=True),
        end_date=TimestampField(to_date=True),
        page=PageField,
        limit=LimitField,
        export=fields.Integer,
    ))
    def get(cls, **kwargs):
        """ 子账号用户维度-划转历史 """
        main_user = g.user
        main_user_id = main_user.id

        if source_user_id := kwargs.get("source_user_id"):
            if main_user_id != source_user_id and not SubAccountManager.has_relationship(
                main_user_id, source_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES):
                raise SubAccountRelationError
        if target_user_id := kwargs.get("target_user_id"):
            if main_user_id != target_user_id and not SubAccountManager.has_relationship(
                main_user_id, target_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES):
                raise SubAccountRelationError
        # sql:1s count(*)
        query = SubAccountAssetTransfer.query.filter(
            SubAccountAssetTransfer.main_user_id == main_user_id,
            SubAccountAssetTransfer.status == SubAccountAssetTransfer.Status.FINISHED,
        ).order_by(SubAccountAssetTransfer.id.desc())
        if source_user_id:
            query = query.filter(SubAccountAssetTransfer.source == source_user_id)
        if target_user_id:
            query = query.filter(SubAccountAssetTransfer.target == target_user_id)
        if asset := kwargs.get('asset'):
            query = query.filter(SubAccountAssetTransfer.asset == asset)
        if start_date := kwargs.get('start_date'):
            query = query.filter(SubAccountAssetTransfer.created_at >= start_date)
        if end_date := kwargs.get('end_date'):
            _next_end_dt = end_date + timedelta(days=1)  # 包含end_date当天内的数据
            query = query.filter(SubAccountAssetTransfer.created_at < _next_end_dt)

        if export := kwargs.get('export'):
            pagination = query.paginate(1, config["EXPORT_ITEM_MAX_COUNT"], error_out=False)
        else:
            pagination = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)

        record = pagination.items
        user_ids = {i.source for i in record}
        user_ids.update({i.target for i in record})
        user_rows = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.name).all()
        user_name_map = {s.id: s.name for s in user_rows}
        user_name_map[main_user_id] = main_user.name_displayed

        items = []
        for i in record:
            item = {
                "time": int(i.created_at.timestamp()),
                "asset": i.asset,
                "amount": i.amount,
                "source_account_type": i.source_account_type,
                "source_user_id": i.source,
                "source_user_name": user_name_map[i.source],
                "target_account_type": i.target_account_type,
                "target_user_id": i.target,
                "target_user_name": user_name_map[i.target],
            }
            items.append(item)

        if export:
            file_name = now().strftime("%Y%m%d-sub-account-transfer-history")
            pref = UserPreferences(main_user_id)
            dt_to_str = partial(datetime_to_str, offset_minutes=pref.timezone_offset)
            for i in items:
                i["time"] = dt_to_str(timestamp_to_datetime(i["time"]))

            fields_ = ["time", "asset", "amount", "source_user_name", "target_user_name"]
            headers = [_("时间"), _("币种"), _("数量"), _("转出"), _("转入")]
            stream = ExcelExporter(
                data_list=items,
                headers=headers,
                fields=fields_,
            ).export_streams()

            return send_file(
                stream,
                download_name=f"{file_name}.xlsx",
                as_attachment=True,
            )

        return dict(
            data=items,
            curr_page=pagination.page,
            has_next=pagination.has_next,
            count=len(items),
            total=pagination.total,
            total_page=pagination.pages,
        )


@ns.route('/<int:user_id>')
@respond_with_code
class SubAccountResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, user_id):
        """ 子账号-详情信息 """
        # TODO：和前端确认没有搜索到该 get 接口
        # TODO：app 没有子账号功能
        main_user: User = g.user
        if not SubAccountManager.has_relationship(main_user.id, user_id, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError

        sub_user: User = User.query.get(user_id)
        sub_account: SubAccount = sub_user.sub_account_ref
        return dict(
            user_name=sub_user.name,
            status=sub_account.status.value,
            remark=sub_account.remark,
            permissions=[i.name for i in sub_account.enum_permissions],
            manager_count=SubAccountManager.get_sub_account_manager_count(user_id),
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            user_name=fields.String,
            remark=fields.String,
            permissions=fields.List(EnumField(SubAccountPermission)),
        )
    )
    def patch(cls, user_id, **kwargs):
        """ 修改子账号信息 """
        main_user: User = g.user
        if not SubAccountManager.has_relationship(main_user.id, user_id, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError

        remark = kwargs.get("remark")
        if remark and len(remark) > 50:
            raise InvalidArgument
        user_name = kwargs.get("user_name")
        permissions = kwargs.get("permissions")

        with CacheLock(LockKeys.sub_account_created(main_user.id)):
            # 这里可能修改user_name 用sub_account_created
            db.session.rollback()
            sub_account = SubAccount.query.filter(
                SubAccount.main_user_id == main_user.id,
                SubAccount.user_id == user_id,
                SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
                SubAccount.is_visible.is_(True),
            ).first()
            if remark:
                sub_account.remark = remark
            if user_name:
                cls.edit_user_name(sub_account, user_name)
            if permissions is not None:
                SubAccountManager.check_hosting(user_id)
                cls.edit_permissions(sub_account, permissions)
            db.session.commit()
            SubAccountInfoCache(user_id).delete()
        return {}

    @classmethod
    def edit_user_name(cls, sub_acc: SubAccount, new_user_name: str):
        if SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.user_id == sub_acc.user_id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).first():
            raise EditManagedSubAccountName

        main_user_id = sub_acc.main_user_id
        mu_model = aliased(User)
        if (
            SubAccount.query.join(User, User.id == SubAccount.user_id)
            .join(mu_model, mu_model.id == SubAccount.main_user_id)
            .filter(mu_model.id == main_user_id,
                    User.name == new_user_name,
                    User.id != sub_acc.user_id,
                    SubAccount.type.in_(SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES),
                    )
            .first()
            is not None
        ):
            raise SubAccountUsernameExists
        sub_user = User.query.get(sub_acc.user_id)
        sub_user.name = new_user_name

    @classmethod
    def edit_permissions(cls, sub_acc: SubAccount, new_permissions: List[SubAccountPermission]):
        if sub_acc.type == SubAccount.Type.CUSTODY:
            raise OperationNotAllowed
        old_permissions = sub_acc.enum_permissions
        asset_rates = PriceManager.assets_to_usd()

        is_close_margin = SubAccountPermission.MARGIN in old_permissions and SubAccountPermission.MARGIN not in new_permissions
        if is_close_margin:
            m_msg = _("关闭杠杆功能时杠杆账户资产需为0 USD，功能关闭后不允许资金转入")
            client = ServerClient()
            accounts_balances_map = client.get_user_accounts_balances(sub_acc.user_id)
            for account_id, assets in accounts_balances_map.items():
                account_id = int(account_id)
                margin_usd = Decimal()
                if SPOT_ACCOUNT_ID < account_id < AssetInvestmentConfig.ACCOUNT_ID:
                    for asset, balances in assets.items():
                        rate = asset_rates.get(asset, Decimal())
                        margin_usd += (balances["available"] + balances["frozen"]) * rate
                    if margin_usd > Decimal():
                        raise InvalidArgument(message=m_msg)

        is_close_perpetual = SubAccountPermission.PERPETUAL in old_permissions and SubAccountPermission.PERPETUAL not in new_permissions
        if is_close_perpetual:
            p_msg = _("关闭合约功能时合约账户资产需为0 USD，功能关闭后不允许资金转入")
            perpetual_usd = Decimal()
            p_client = PerpetualServerClient()
            perpetual_balances = p_client.get_user_balances(sub_acc.user_id)
            for asset, balance in perpetual_balances.items():
                equity = balance["balance_total"]
                perpetual_usd += equity * asset_rates.get(asset, Decimal())
            if perpetual_usd > Decimal():
                raise InvalidArgument(message=p_msg)

            position_market_map = {i["market"]: i for i in p_client.position_pending(sub_acc.user_id)}
            p_markets = PerpetualMarketCache().get_market_list()
            for m, position_data in position_market_map.items():
                if m in p_markets and position_data:
                    raise InvalidArgument(message=p_msg)

        is_close_amm = SubAccountPermission.AMM in old_permissions and SubAccountPermission.AMM not in new_permissions
        if is_close_amm:
            amm_msg = _("关闭AMM时AMM账户资产需为0 USD，功能关闭后不允许增加流动性")
            if UserLiquidity.query.filter(
                UserLiquidity.user_id == sub_acc.user_id,
                UserLiquidity.liquidity > 0,
            ).first():
                raise InvalidArgument(message=amm_msg)

        is_close_api = SubAccountPermission.API in old_permissions and SubAccountPermission.API not in new_permissions
        if is_close_api:
            # `AIP管理`权限关闭，关闭后已创建的全部API自动失效
            apis = ApiAuth.query.filter(
                ApiAuth.user_id == sub_acc.user_id,
                ApiAuth.status == ApiAuth.Status.VALID,
            )
            for row in apis:
                row.status = ApiAuth.Status.DELETED
                ApiAuthCache(row.access_id).delete()

        sub_acc.permissions = json.dumps([i.name for i in new_permissions])


@ns.route('/<int:user_id>/frozen')
@respond_with_code
class AccountFreezingResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def put(cls, user_id):
        """ 禁用子账号 """
        main_user: User = g.user
        if not SubAccountManager.has_relationship(main_user.id, user_id, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError
        SubAccountManager.freeze_account(user_id)


@ns.route('/<int:user_id>/unfrozen')
@respond_with_code
class AccountUnfreezingResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def put(cls, user_id):
        """ 取消禁用子账号 """
        main_user: User = g.user
        if not SubAccountManager.has_relationship(main_user.id, user_id, SubAccountManager.USER_OPERABLE_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError
        SubAccountManager.unfreeze_account(user_id)


@ns.route('/reference_user')
@respond_with_code
class ReferenceUser(Resource):

    @classmethod
    @require_login
    def get(cls):
        user: User = g.user
        main_user: User = user.sub_account_ref.main_user
        return dict(
            reference_id=main_user.id,
            email=main_user.email,
            name=main_user.nickname
        )


@ns.route("/managers")
@respond_with_code
class SubAccountManagerListResource(Resource):
    @classmethod
    @require_2fa(MobileCodeType.SUB_ACCOUNT_BIND_MANAGER, allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            account=EmailField(required=True),
            manage_type=EnumField(
                SubAccountManagerRelation.ManageType,
                missing=SubAccountManagerRelation.ManageType.MANAGED
            ),
            remark=fields.String,
        )
    )
    def post(cls, **kwargs):
        """ 子账号-新增授权 """
        sub_user_id = kwargs["user_id"]
        email = kwargs["account"]
        remark = kwargs.get("remark") or ""
        manage_type = kwargs['manage_type']

        main_user: User = g.user
        if not SubAccountManager.has_relationship(main_user.id, sub_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES):
            raise SubAccountRelationError

        manage_user: User = User.query.filter(User.email == email).first()
        if not manage_user:
            msg = _("被授权人不存在，请重新确认账号。")
            if manage_type == SubAccountManagerRelation.ManageType.HOSTING:
                msg = _("托管人不存在，请重新确认账号。")
            raise InvalidArgument(message=msg)
        if manage_user.is_sub_account:
            raise InvalidArgument
        if manage_user.id == main_user.id:
            msg = _("当前子账号为本人创建，请授权其他主账号管理")
            if manage_type == SubAccountManagerRelation.ManageType.HOSTING:
                msg = _("当前子账号为本人创建，请托管其他主账号管理")
            raise InvalidArgument(message=msg)
        if manage_type == SubAccountManagerRelation.ManageType.HOSTING:
            if manage_user.user_type not in [
                User.UserType.INTERNAL_MAKER,
                User.UserType.EXTERNAL_MAKER,
                User.UserType.EXTERNAL_SPOT_MAKER,
                User.UserType.EXTERNAL_CONTRACT_MAKER,
            ]:
                raise InvalidArgument(message=_("请输入做市商类型的账号"))

        with CacheLock(LockKeys.sub_account_operation(sub_user_id)):
            db.session.rollback()
            sub_account = SubAccountManager.get_valid_sub_account(
                main_user.id, sub_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
            if not sub_account:
                raise InvalidArgument

            cur_manager_count = SubAccountManager.get_sub_account_manager_count(sub_user_id)
            cur_hosting_count = SubAccountManager.get_sub_account_manager_count(
                sub_user_id, SubAccountManagerRelation.ManageType.HOSTING
            )
            if manage_type == SubAccountManagerRelation.ManageType.HOSTING:
                if cur_hosting_count > 0:
                    raise InvalidArgument(message=_("托管中：无法授权，无法重复托管"))
                if cur_manager_count > 0:
                    raise InvalidArgument(message=_("授权中：可重复授权，无法创建托管"))
            else:
                if cur_hosting_count > 0:
                    raise InvalidArgument(message=_("托管中：无法授权，无法重复托管"))
                if cur_manager_count >= SUB_ACCOUNT_MANAGER_NUM_LIMIT:
                    raise SubAccountManagerBeyondAmountLimit(num=SUB_ACCOUNT_MANAGER_NUM_LIMIT)

            relation: SubAccountManagerRelation = SubAccountManagerRelation.get_or_create(
                user_id=sub_user_id,
                manager_id=manage_user.id,
            )
            if relation.id and relation.status == SubAccountManagerRelation.Status.VALID:
                raise InvalidArgument(message=_("已存在授权关系，请勿重复授权"))
            relation.main_user_id = main_user.id
            relation.manage_type = manage_type
            relation.status = SubAccountManagerRelation.Status.VALID
            relation.main_user_remark = remark
            if manage_type == SubAccountManagerRelation.ManageType.HOSTING:
                cls._do_hosting_action_before_create(sub_account)
                manage_status = SubAccount.ManageStatus.HOSTING
                db.session.add(
                    SubAccountManagerRelationHistory(
                        user_id=sub_user_id,
                        manager_id=manage_user.id,
                        managed_at=now(),
                    )
                )
            else:
                manage_status = SubAccount.ManageStatus.MANAGED
            sub_account.manage_status = manage_status
            db.session.add(relation)
            db.session.commit()

            SubAccountInfoCache(sub_user_id).delete()
            send_sub_account_manage_notice_email.delay(relation.id)

    @classmethod
    def _do_hosting_action_before_create(cls, sub_account: SubAccount):
        # init permissions
        sub_account.permissions = json.dumps([i.name for i in SubAccountPermission])
        # `AIP管理`权限关闭，关闭后已创建的全部API自动失效
        apis = ApiAuth.query.filter(
            ApiAuth.user_id == sub_account.user_id,
            ApiAuth.status == ApiAuth.Status.VALID,
        )
        for row in apis:
            row.status = ApiAuth.Status.DELETED
            ApiAuthCache(row.access_id).delete()

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 子账号-授权列表 """
        main_user = g.user
        query = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.main_user_id == main_user.id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).order_by(SubAccountManagerRelation.id.desc())
        records = query.paginate(kwargs["page"], kwargs["limit"])
        sub_balance_map = get_sub_account_balance_map(main_user.id)
        items = cls.build_manager_list(records.items, sub_balance_map)
        return dict(
            data=items,
            curr_page=records.page,
            has_next=records.has_next,
            count=len(items),
            total=records.total,
            total_page=records.pages,
        )

    @classmethod
    def build_manager_list(cls, records: List, sub_balance_map: dict[int, Decimal]) -> List[Dict]:
        sub_user_ids = {i.user_id for i in records}
        sub_accounts = SubAccount.query.filter(
            SubAccount.user_id.in_(sub_user_ids),
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).all()
        sub_acc_map = {i.user_id: i for i in sub_accounts}

        user_ids = sub_user_ids | {i.manager_id for i in records}
        user_query = (
            User.query.filter(
                User.id.in_(user_ids),
            )
            .with_entities(
                User.id,
                User.name,
                User.email,
            )
            .all()
        )
        user_info_map = {i.id: i for i in user_query}

        items = []

        for i in records:
            sub_acc = sub_acc_map[i.user_id]
            sub_user_info = user_info_map[i.user_id]
            manager_info = user_info_map[i.manager_id]
            item = {
                "id": i.id,
                "user_id": i.user_id,
                "user_name": sub_user_info.name,
                "balance_usd": sub_balance_map.get(i.user_id, Decimal()),
                "manager_id": i.manager_id,
                "manage_type": i.manage_type.name,
                "manager_name": manager_info.email,
                "permissions": [i.name for i in sub_acc.enum_permissions],
                "remark": i.main_user_remark,
            }
            items.append(item)
        return items


@ns.route("/managers/<int:row_id>")
@respond_with_code
class SubAccountManagerResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def delete(cls, row_id):
        """ 子账号-解除授权 """
        main_user_id = g.user.id
        relation = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.id == row_id,
            SubAccountManagerRelation.main_user_id == main_user_id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).first()
        if not relation:
            raise InvalidArgument

        sub_user_id = relation.user_id
        with CacheLock(LockKeys.sub_account_operation(sub_user_id)):
            db.session.rollback()
            sub_account = SubAccountManager.get_valid_sub_account(
                main_user_id, sub_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
            if not sub_account:
                raise InvalidArgument

            relation.status = SubAccountManagerRelation.Status.DELETED
            db.session.add(relation)

            if relation.manage_type == SubAccountManagerRelation.ManageType.MANAGED:
                if not SubAccountManagerRelation.query.filter(
                    SubAccountManagerRelation.id != relation.id,
                    SubAccountManagerRelation.user_id == sub_user_id,
                    SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
                ).first():
                    sub_account.manage_status = SubAccount.ManageStatus.UNMANAGED
                db.session.commit()
            else:
                sub_account.manage_status = SubAccount.ManageStatus.CANCEL_HOSTING
                sub_account.is_visible = False  # 暂时隐藏，无法使用
                apis = ApiAuth.query.filter(
                    ApiAuth.user_id == sub_account.user_id,
                    ApiAuth.status == ApiAuth.Status.VALID,
                )
                for row in apis:
                    row.status = ApiAuth.Status.DELETED
                    ApiAuthCache(row.access_id).delete()
                relation_history = SubAccountManagerRelationHistory.query.filter(
                    SubAccountManagerRelationHistory.user_id == sub_account.user_id,
                    SubAccountManagerRelationHistory.manager_id == relation.manager_id,
                    SubAccountManagerRelationHistory.unmanaged_at.is_(None),
                ).first()
                relation_history.unmanaged_at = now()
                log = SubAccountClearLog(
                    user_id=sub_account.user_id,
                    main_user_id=main_user_id,
                )
                db.session.add(log)
                db.session.commit()
                execute_subuser_clean_log_task.delay(log.id)

            SubAccountInfoCache(sub_user_id).delete()
            send_sub_account_manage_notice_email.delay(relation.id)

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            remark=fields.String(required=True),
        )
    )
    def put(cls, row_id, **kwargs):
        """ 子账号-授权列表 编辑备注 """
        main_user_id = g.user.id
        relation = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.id == row_id,
            SubAccountManagerRelation.main_user_id == main_user_id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).first()
        if not relation:
            raise InvalidArgument

        sub_user_id = relation.user_id
        sub_account = SubAccountManager.get_valid_sub_account(
            main_user_id, sub_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
        if not sub_account:
            raise InvalidArgument

        relation.main_user_remark = kwargs["remark"]
        db.session.commit()


@ns.route("/managements")
@respond_with_code
class SubAccountManagementListResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 子账号-托管列表(他人授权) """
        manage_user = g.user
        query = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.manager_id == manage_user.id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).order_by(SubAccountManagerRelation.id.desc())
        records = query.paginate(kwargs["page"], kwargs["limit"])
        sub_balance_map = cls._get_sub_balance_map_by_relation(records.items)
        items = cls.build_management_list(records.items, sub_balance_map)
        return dict(
            data=items,
            curr_page=records.page,
            has_next=records.has_next,
            count=len(items),
            total=records.total,
            total_page=records.pages,
        )

    @classmethod
    def _get_sub_balance_map_by_relation(cls, items: list[SubAccountManagerRelation]) -> dict[int, Decimal]:
        main_to_sub = defaultdict(set)
        for item in items:
            main_to_sub[item.main_user_id].add(item.user_id)

        ret = {}
        for main_uid, sub_uids in main_to_sub.items():
            sub_balance_map = get_sub_account_balance_map(main_uid, list(sub_uids))
            ret.update(sub_balance_map)
        return ret

    @classmethod
    def build_management_list(cls, records: List, sub_balance_map: dict[int, Decimal]) -> List[Dict]:
        sub_user_ids = {i.user_id for i in records}
        sub_accounts = SubAccount.query.filter(
            SubAccount.user_id.in_(sub_user_ids),
            SubAccount.type == SubAccount.Type.NORMAL,
            SubAccount.is_visible.is_(True),
        ).all()
        sub_acc_map = {i.user_id: i for i in sub_accounts}

        user_ids = sub_user_ids | {i.main_user_id for i in records}
        user_query = (
            User.query.filter(
                User.id.in_(user_ids),
            )
            .with_entities(
                User.id,
                User.name,
                User.email,
            )
            .all()
        )
        user_info_map = {i.id: i for i in user_query}

        items = []
        main_user_ids = [v.main_user_id for v in records]
        disabled_main_ids = UserConfigKeyCache.get_login_disabled_by_admin_ids(main_user_ids)
        disabled_main_ids |= UserVisitPermissionCache().check_users_permission(
            main_user_ids,
            [UserVisitPermissionCache.FORBIDDEN_VALUE]
        )
        for i in records:
            sub_acc = sub_acc_map[i.user_id]
            sub_user_info = user_info_map[i.user_id]
            main_user_info = user_info_map[i.main_user_id]
            item = {
                "id": i.id,
                "user_id": i.user_id,
                "user_name": sub_user_info.name,
                "balance_usd": sub_balance_map.get(i.user_id, Decimal()),
                "manage_type": i.manage_type.name,
                "main_user_id": i.main_user_id,
                "main_user_name": main_user_info.email,
                "switchable": i.main_user_id not in disabled_main_ids,
                "permissions": [i.name for i in sub_acc.enum_permissions],
                "remark": i.manager_remark,
            }
            items.append(item)
        return items


@ns.route("/managements/<int:row_id>")
@respond_with_code
class SubAccountManagementResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            remark=fields.String(required=True),
        )
    )
    def put(cls, row_id, **kwargs):
        """ 子账号-托管列表(他人授权) 编辑备注 """
        manage_user_id = g.user.id
        relation = SubAccountManagerRelation.query.filter(
            SubAccountManagerRelation.id == row_id,
            SubAccountManagerRelation.manager_id == manage_user_id,
            SubAccountManagerRelation.status == SubAccountManagerRelation.Status.VALID,
        ).first()
        if not relation:
            raise InvalidArgument

        sub_user_id = relation.user_id
        sub_account = SubAccountManager.get_valid_sub_account(
            relation.main_user_id, sub_user_id, SubAccountManager.ONLY_NORMAL_SUB_ACCOUNT_TYPES)
        if not sub_account:
            raise InvalidArgument

        relation.manager_remark = kwargs["remark"]
        db.session.commit()
