from decimal import Decimal

from marshmallow import fields

from sqlalchemy import func

from app.api.common import Namespace
from app.api.common import Resource
from app.api.common import respond_with_code
from app.api.common.fields import EnumField
from app.api.common.fields import <PERSON>Field
from app.api.common.fields import LimitField
from app.api.common.fields import Timestamp<PERSON>ield
from app.api.common.fields import PositiveDecimalField

from app.business.prices import PriceManager
from app.business.clients.wallet import WalletClient
from app.business.onchain.base import OnchainAddressHelper

from app.common.onchain import Chain
from app.common.onchain import CHAIN_MONEY_MAPPING

from app.utils.onchain import decimal_mul
from app.utils.onchain import decimal_sub
from app.utils.onchain import amount_to_str

from app.models.onchain import OnchainToken
from app.models.onchain import OnchainAssetLiability

from app.caches.onchain import OnchainTokenQuoteCache

ns = Namespace('Onchain - Statistic')


@ns.route('/wallet-assets')
@respond_with_code
class OnchainWalletAssetsResource(Resource):

    native_symbol_map = {
        Chain.SOL: 'SOL',
        Chain.ERC20: 'ETH',
        Chain.BSC: 'BNB',
    }
    native_name_map = {
        Chain.SOL: 'Solana',
        Chain.ERC20: 'Ethereum',
        Chain.BSC: 'Binance Coin',
    }

    @classmethod
    def _get_native_price(cls, chain: Chain) -> Decimal:
        return PriceManager().asset_to_usd(cls.native_symbol_map[chain]) or Decimal()

    @classmethod
    def get(cls):
        """统计-资产统计-链上Bot持仓统计"""
        wallet_assets = WalletClient().get_onchain_asset_statistics()

        usdt_map = {
            CHAIN_MONEY_MAPPING[Chain.SOL]['USDT']['contract'],
            CHAIN_MONEY_MAPPING[Chain.ERC20]['USDT']['contract'],
            CHAIN_MONEY_MAPPING[Chain.BSC]['USDT']['contract'],
        }

        token_map = {
            chain: {
                item.contract: item for item in OnchainToken.query.filter(
                    OnchainToken.chain == chain,
                ).all()
            } for chain in Chain
        }
        token_ids = [token.id for _, contract_token_map in token_map.items() for _, token in contract_token_map.items()]
        token_quote_map = OnchainTokenQuoteCache().get_many(token_ids)

        data = []
        for item in wallet_assets:
            if not Decimal(item['amount']):
                continue
            chain = {
                'SOL': Chain.SOL,
                'ERC20': Chain.ERC20,
                'BSC': Chain.BSC,
            }.get(item['chain'])
            if not chain:
                continue
            if not item['identity']:
                # 原生代币合约地址为空
                contract = '--'
                symbol = cls.native_symbol_map[chain]
                name = cls.native_name_map[chain]
                price = cls._get_native_price(chain)
            else:
                contract = OnchainAddressHelper(chain).normalise_address(item['identity'])
                contract_token_map = token_map[chain]
                if contract not in contract_token_map:
                    symbol = '未匹配'
                    name = '未匹配'
                    price = Decimal()
                else:
                    token = contract_token_map[contract]
                    symbol = token.symbol
                    name = token.name
                    quote = token_quote_map.get(token.id, {})
                    price = quote.get('price', Decimal())
            data.append(dict(
                chain=chain,
                contract=contract,
                symbol=symbol,
                name=name,
                amount=item['amount'],
                volume=decimal_mul(item['amount'], price),
            ))

        data.sort(key=lambda x: (x['contract'] != '--', x['contract'] not in usdt_map, -Decimal(x['volume'])))
        return dict(
            items=data,
            chains=[item.name for item in Chain],
        )


@ns.route('/asset-liability')
@respond_with_code
class OnchainAssetLiabilityResource(Resource):

    @classmethod
    def one_token(
            cls, chain: Chain, contract: str, start_time: int | None, end_time: int | None, page: int, limit: int
    ) -> (list[OnchainAssetLiability], int):
        """搜索单个币种时, 支持时间范围以及分页查询"""
        if chain:
            token_query = OnchainToken.query.filter(
                OnchainToken.chain == chain,
                OnchainToken.contract == OnchainAddressHelper(chain).normalise_address(contract),
            )
        else:
            token_query = OnchainToken.query.filter(
                OnchainToken.contract == contract,
            )
        token_ids = [token.id for token in token_query.all()]
        if len(token_ids) == 0:
            return [], 0
        query = OnchainAssetLiability.query.filter(
            OnchainAssetLiability.token_id.in_(token_ids),
        )
        if start_time:
            query = query.filter(
                OnchainAssetLiability.created_at >= start_time,
            )
        if end_time:
            query = query.filter(
                OnchainAssetLiability.created_at < end_time,
            )
        query = query.order_by(OnchainAssetLiability.id.desc())
        pagination = query.paginate(page, limit, error_out=False)
        return pagination.items, pagination.total

    @classmethod
    def all(cls, min_usd: Decimal | None) -> (list[OnchainAssetLiability], int):
        last_records = OnchainAssetLiability.query.filter().with_entities(
            func.max(OnchainAssetLiability.id).label('id')
        ).group_by(
            OnchainAssetLiability.token_id,
        ).all()
        query = OnchainAssetLiability.query.filter(
            OnchainAssetLiability.id.in_([r.id for r in last_records]),
        )
        query = query.order_by(OnchainAssetLiability.id.desc())
        records = [item for item in query.all() if Decimal(item.wallet_balance) or Decimal(item.user_balance)]
        if not min_usd:
            return records, len(records)
        token_ids = [item.token_id for item in records]
        token_quote_map = OnchainTokenQuoteCache().get_many(token_ids)
        data = []
        for item in records:
            item: OnchainAssetLiability
            diff_amount = decimal_sub(item.wallet_balance, item.user_balance)
            diff_volume = decimal_mul(diff_amount, token_quote_map.get(item.token_id, {}).get('price', Decimal()))
            if abs(diff_volume) >= min_usd:
                data.append(item)
        return data, len(data)

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain, allow_none=True),
        contract=fields.String(allow_none=True),
        min_usd=PositiveDecimalField,
        start_time=TimestampField(is_ms=True),
        end_time=TimestampField(is_ms=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """统计-资产统计-链上资产负债统计"""
        chain = kwargs.get('chain')
        contract = kwargs.get('contract')
        if contract:
            items, total = cls.one_token(
                chain,
                contract,
                kwargs.get('start_time'),
                kwargs.get('end_time'),
                kwargs.get('page'),
                kwargs.get('limit'),
            )
        else:
            items, total = cls.all(kwargs.get('min_usd'))

        token_map = {
            token.id: token for token in OnchainToken.query.filter(
                OnchainToken.id.in_({item.token_id for item in items})
            )
        }

        data = []
        quote_data = OnchainTokenQuoteCache().get_many([item.token_id for item in items])
        for item in items:
            if item.token_id not in token_map:
                continue
            token: OnchainToken = token_map[item.token_id]
            if chain and token.chain != chain:
                continue
            diff = decimal_sub(item.wallet_balance, item.user_balance)
            price = quote_data.get(item.token_id, {}).get('price', Decimal()) or Decimal()
            data.append(dict(
                chain=token.chain.name,
                contract=token.contract,
                symbol=token.symbol,
                name=token.name,
                wallet_balance=item.wallet_balance,
                user_balance=item.user_balance,
                diff=amount_to_str(diff, 8),
                diff_usd=decimal_mul(diff, price),
                created_at=item.created_at,
            ))
        return dict(
            items=data,
            total=total,
            chains=[item.name for item in Chain],
        )
