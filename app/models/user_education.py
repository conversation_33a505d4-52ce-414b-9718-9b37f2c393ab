from enum import Enum

from pydantic import BaseModel

from app import Language
from app.models.base import ModelBase, db
from app.utils import AWSBucketPublic
from app.utils.file import get_video_thumbnail_size_key, ThumbnailScale


class Content(BaseModel):
    id: str
    title: str
    desc: str
    created_at: int

    def is_completed(self) -> bool:
        return bool(self.title and self.desc)

    def fmt(self):
        return {
            'title': self.title,
            'desc': self.desc,
            'created_at': self.created_at,
        }


class OPInstruction(Content):
    pass


class VideoTutorial(Content):

    def is_completed(self) -> bool:
        return bool(self.title)

    def fmt(self):
        return {
            'title': self.title,
            'key': self.desc,
            'url': AWSBucketPublic.get_file_url(self.desc),
            'thumbnail_url': AWSBucketPublic.get_file_url(
                get_video_thumbnail_size_key(self.desc, ThumbnailScale(ThumbnailScale.Size.ORIGINAL))
            ),
            'created_at': self.created_at,
        }


class FeatureOverview(Content):

    def fmt(self):
        return {
            'title': self.title,
            'url': self.desc,
            'created_at': self.created_at,
        }


class Faq(Content):

    def fmt(self):
        return {
            'title': self.title,
            'url': self.desc,
            'created_at': self.created_at,
        }


class FaqExtra(BaseModel):
    more: str


class UserEducationContent(ModelBase):

    class ContentType(Enum):
        OPInstruction = 'op_instruction'
        VideoTutorial = 'video_tutorial'
        FeatureOverview = 'feature_overview'
        Faq = 'faq'

    ContentMap = {
        ContentType.OPInstruction: OPInstruction,
        ContentType.VideoTutorial: VideoTutorial,
        ContentType.FeatureOverview: FeatureOverview,
        ContentType.Faq: Faq,
    }

    ExtraMap = {
        ContentType.Faq: FaqExtra,
    }

    user_education_id = db.Column(db.Integer, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    content_type = db.Column(db.StringEnum(ContentType), nullable=False)
    contents = db.Column(db.MYSQL_JSON(Content), default=[])
    extra = db.Column(db.JSON, default={})

    def get_clean_empty_contents(self):
        res = []
        for content in self.contents:
            if not content.is_completed():
                continue
            res.append(content)

        return res

    def fmt_contents(self):
        base_model = self.ContentMap[self.content_type]
        return [
            base_model(
                **content.dict()
            ).fmt()
            for content in self.get_clean_empty_contents()
        ]

    def to_dict(self, *, with_hook: bool = True, enum_to_name: bool = False):
        res = super().to_dict(with_hook=with_hook, enum_to_name=enum_to_name)
        res['contents'] = [c.dict() for c in res['contents'] if isinstance(c, Content)]
        return res


class UserEducation(ModelBase):

    class Platform(Enum):
        APP = "app"
        WEB = "web"

    class Path(Enum):
        SPOT_TRADE_SPOT = '现货交易页-现货'
        SPOT_TRADE_MARGIN = '现货交易页-杠杆'
        PERPETUAL_TRADE = '合约交易页'
        ONCHAIN = '链上交易页'
        OTHER = '其他'

    class Status(Enum):
        DELETED = "deleted"
        ACTIVE = 'active'
        INACTIVE = 'inactive'

    platform = db.Column(db.StringEnum(Platform), nullable=False)
    path = db.Column(db.StringEnum(Path), nullable=False)
    desc = db.Column(db.String(1024), comment="备注")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.INACTIVE)
    operator = db.Column(db.Integer, nullable=False)
    last_active_time = db.Column(db.MYSQL_DATETIME_6, nullable=True)
