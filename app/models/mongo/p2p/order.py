from app.models import db
from app.models.base import M2MModelBase


class P2pOrderCreateSnapMySQL(M2MModelBase):
    """MySQL 版本的 P2P 订单创建快照模型"""
    __tablename__ = 'p2p_order_create_snap'

    order_id = db.Column(db.Integer, nullable=False, unique=True, index=True)
    adv = db.Column(db.MYSQL_JSON)
    pay_channel = db.Column(db.MYSQL_JSON)
    user_info = db.Column(db.MYSQL_JSON)

    @classmethod
    def get_by_order_id(cls, order_id):
        """根据订单ID获取快照, 函数对象不会被 session 跟踪"""
        item = cls.query.filter(cls.order_id == order_id).first()
        if item and item.user_info:
            db.session.expunge(item)
            # 确保用户信息的键是整数类型
            item.user_info = {int(k): v for k, v in item.user_info.items()}
        return item

    @classmethod
    def get_by_order_ids(cls, order_ids):
        """根据订单ID列表获取快照, 函数对象不会被 session 跟踪"""
        items = cls.query.filter(cls.order_id.in_(order_ids)).all()
        for item in items:
            if item.user_info:
                db.session.expunge(item)
                # 确保用户信息的键是整数类型
                item.user_info = {int(k): v for k, v in item.user_info.items()}
        return items