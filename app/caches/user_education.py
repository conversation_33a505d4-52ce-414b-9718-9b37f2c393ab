import json
from collections import defaultdict
from typing import List

from app import Language
from app.caches import Set<PERSON>ache, HashCache
from app.models.media import VideoSubtitle, Video
from app.models.user_education import UserEducation, UserEducationContent
from app.utils import AWSBucketPublic
from app.utils.parser import JsonEncoder


class UserEducationCache(HashCache):
    ttl = 86400 * 10

    def __init__(self, platform, path):
        self.platform = platform
        self.path = path
        super().__init__(f"{platform}-{path}")

    def reload(self):
        if self.path == UserEducation.Path.OTHER:
            return {}

        user_education = UserEducation.query.filter(
            UserEducation.platform == self.platform,
            UserEducation.path == self.path,
            UserEducation.status == UserEducation.Status.ACTIVE,
        ).order_by(
            UserEducation.updated_at.desc()
        ).first()

        if user_education is None:
            self.delete()
            return {}

        user_education_lang_content = defaultdict(dict)
        for content in UserEducationContent.query.filter(
            UserEducationContent.user_education_id == user_education.id
        ).all():
            user_education_lang_content[content.lang][content.content_type] = content

        data = {}
        for lang in Language:
            user_education_lang_data = {}
            has_contents = False
            for content_type in [
                UserEducationContent.ContentType.OPInstruction,
                UserEducationContent.ContentType.VideoTutorial,
                UserEducationContent.ContentType.FeatureOverview,
                UserEducationContent.ContentType.Faq,
            ]:
                content = user_education_lang_content.get(
                    lang, {}
                ).get(content_type)

                cache_content_data = {'contents': []}
                if content:
                    contents = content.fmt_contents()
                    cache_content_data['contents'] = contents
                    if contents:
                        has_contents = True
                        if content_type == UserEducationContent.ContentType.VideoTutorial:
                            # 批量填充视频字幕
                            video_keys = [content['key'] for content in contents if content.get('key')]

                            video_key_id_map = {
                                video.file_key: video.id
                                for video in Video.query.filter(
                                    Video.file_key.in_(video_keys)
                                ).all()
                            }

                            video_ids = list(video_key_id_map.values())
                            subtitles = VideoSubtitle.query.filter(
                                VideoSubtitle.video_id.in_(video_ids), VideoSubtitle.lang == lang
                            ).all()
                            subtitle_keys = {subtitle.video_id: subtitle.file_key for subtitle in subtitles}

                            if lang != Language.EN_US:
                                # 填充默认字幕
                                for default_subtitle in VideoSubtitle.query.filter(
                                    VideoSubtitle.video_id.in_(video_ids), VideoSubtitle.lang == Language.EN_US
                                ).all():
                                    if default_subtitle.video_id not in subtitle_keys:
                                        subtitle_keys[default_subtitle.video_id] = default_subtitle.file_key

                            for _content in contents:
                                _content['subtitle_key'] = ''
                                _content['subtitle_url'] = ''
                                if video_key := _content.get('key'):
                                    if video_id := video_key_id_map.get(video_key):
                                        if subtitle_key := subtitle_keys.get(video_id):
                                            _content['subtitle_key'] = subtitle_key
                                            _content['subtitle_url'] = AWSBucketPublic.get_file_url(subtitle_key)

                    if content.extra:
                        cache_content_data.update(content.extra)
                        if any(content.extra.values()):
                            has_contents = True

                user_education_lang_data[content_type.value] = cache_content_data

            if not has_contents:
                continue

            data[lang.name] = user_education_lang_data

        for lang, ue_dict in data.items():
            data[lang] = json.dumps(ue_dict, cls=JsonEncoder)
        self.save_by_ttl(data)
        return data

    def read_by_lang(self, lang):
        data = self.hget(lang.name)
        return json.loads(data) if data else {}

    def save_by_ttl(self, data):
        self.save(data)
        self.expire(self.ttl)


class UserEducationSetCache(SetCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def fmt_key(cls, path: UserEducation.Path | str, platform: UserEducation.Platform | str, lang: Language):
        if isinstance(path, UserEducation.Path):
            path = path.name
        if isinstance(platform, UserEducation.Platform):
            platform = platform.name
        return f"{path}-{platform}-{lang.name}"

    def check_exists(
        self, lang: Language, platform: UserEducation.Platform, paths: List[UserEducation.Path]
    ) -> List[UserEducation.Path]:
        keys = []
        for path in paths:
            keys.append(self.fmt_key(path.name, platform.name, lang))
        res = []
        for idx, existed in enumerate(self.smismember(keys)):
            if existed:
                res.append(paths[idx])

        return res

    def update_user_education_set(
        self, platform: UserEducation.Platform, path: UserEducation.Path,
        add_langs: List[Language], remove_langs: List[Language]
    ):
        add_keys = [
            self.fmt_key(path, platform, lang)
            for lang in add_langs
        ]
        remove_keys = [
            self.fmt_key(path, platform, lang)
            for lang in remove_langs
        ]

        if add_keys:
            self.sadd(*add_keys)
        if remove_keys:
            self.srem(*remove_keys)
